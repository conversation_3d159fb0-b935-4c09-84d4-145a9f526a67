# 🔐 Comptes de test - RH Manager IA

## Comptes disponibles

### 👑 Administrateur Système
- **Email** : `<EMAIL>`
- **Mot de passe** : `admin123`
- **Rôle** : ADMIN
- **Accès** : Complet (toutes les fonctionnalités)

### 👥 Responsable RH
- **Email** : `<EMAIL>`
- **Mot de passe** : `rh123`
- **Nom** : <PERSON>
- **Rôle** : RH
- **Accès** : Gestion des attestations, employés, demandes

### 🏢 Manager
- **Email** : `<EMAIL>`
- **Mot de passe** : `manager123`
- **Nom** : <PERSON>
- **Rôle** : MANAGER
- **Accès** : Validation des demandes de son équipe

### 💻 Employé
- **Email** : `<EMAIL>`
- **Mot de passe** : `employe123`
- **Nom** : <PERSON>
- **Rôle** : EMPLOYE
- **Accès** : Création de demandes d'attestations

## 🚀 Test de l'IA OpenAI

Pour tester la génération d'attestations avec l'IA :

1. **Connectez-vous** avec le compte RH ou Admin
2. **Configurez votre clé OpenAI** dans `.env.local` :
   ```
   OPENAI_API_KEY="sk-votre-cle-api-ici"
   ```
3. **Allez dans "Configuration IA"** pour vérifier la connexion
4. **Créez des attestations** dans la section "Attestations"

## 📝 Notes

- Tous les comptes ont été créés avec des données de test
- Les mots de passe sont hashés avec bcrypt
- La base de données peut être réinitialisée en relançant le seed
