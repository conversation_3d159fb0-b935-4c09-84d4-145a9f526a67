import { AppSidebar } from "@/components/app-sidebar"
import { SiteHeader } from "@/components/site-header"
import { DemandesTable } from "@/components/demandes-table"
import { CreateDemandeForm } from "@/components/create-demande-form"
import {
  SidebarInset,
  SidebarProvider,
} from "@/components/ui/sidebar"
import { getDemandes, getUsers } from "@/lib/data"

export default async function DemandesPage() {
  const [demandes, users] = await Promise.all([
    getDemandes(),
    getUsers(),
  ])
  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
          "--header-height": "calc(var(--spacing) * 12)",
        } as React.CSSProperties
      }
    >
      <AppSidebar variant="inset" />
      <SidebarInset>
        <SiteHeader />
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
              <div className="px-4 lg:px-6">
                <div className="flex flex-col gap-4">
                  <div>
                    <h1 className="text-2xl font-bold">Gestion des Demandes</h1>
                    <p className="text-muted-foreground">
                      Gérez toutes les demandes des employés : congés, matériel, attestations
                    </p>
                  </div>
                  <CreateDemandeForm users={users} />
                  <DemandesTable data={demandes} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
