"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { IconClock, IconCheck, IconX, IconEye, IconUser } from "@tabler/icons-react"
import Link from "next/link"

interface RecentDemandesProps {
  demandes: any[] // Type temporaire, on affinera plus tard
}

export function RecentDemandes({ demandes }: RecentDemandesProps) {
  // Prendre les 5 demandes les plus récentes
  const demandesRecentes = demandes
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, 5)

  const getStatutBadge = (status: string) => {
    switch (status) {
      case "pending":
        return <Badge variant="outline" className="text-orange-600">En attente</Badge>
      case "approved":
        return <Badge variant="outline" className="text-green-600">Approuvée</Badge>
      case "rejected":
        return <Badge variant="outline" className="text-red-600">Rejetée</Badge>
      case "in_progress":
        return <Badge variant="outline" className="text-blue-600">En cours</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getTypeBadge = (type: string) => {
    switch (type) {
      case "conge":
        return <Badge variant="secondary">Congé</Badge>
      case "materiel":
        return <Badge variant="secondary">Matériel</Badge>
      case "attestation":
        return <Badge variant="secondary">Attestation</Badge>
      default:
        return <Badge variant="secondary">{type}</Badge>
    }
  }

  const getStatutIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <IconClock className="h-4 w-4 text-orange-500" />
      case "approved":
        return <IconCheck className="h-4 w-4 text-green-500" />
      case "rejected":
        return <IconX className="h-4 w-4 text-red-500" />
      case "in_progress":
        return <IconClock className="h-4 w-4 text-blue-500" />
      default:
        return <IconClock className="h-4 w-4" />
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Demandes récentes</CardTitle>
            <CardDescription>
              Les dernières demandes soumises par les employés
            </CardDescription>
          </div>
          <Link href="/dashboard/demandes">
            <Button variant="outline" size="sm">
              <IconEye className="mr-2 h-4 w-4" />
              Voir tout
            </Button>
          </Link>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {demandesRecentes.map((demande) => (
            <div key={demande.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors">
              <div className="flex items-center gap-3">
                {getStatutIcon(demande.status)}
                <div className="flex flex-col">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{demande.user?.name || 'Utilisateur inconnu'}</span>
                    {getTypeBadge(demande.type)}
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <IconUser className="h-3 w-3" />
                    <span>{demande.user?.departement || 'Non défini'}</span>
                    <span>•</span>
                    <span>{new Date(demande.createdAt).toLocaleDateString('fr-FR')}</span>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                {getStatutBadge(demande.status)}
                {demande.priorite === "URGENTE" && (
                  <Badge variant="destructive" className="text-xs">
                    Urgent
                  </Badge>
                )}
              </div>
            </div>
          ))}

          {demandesRecentes.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              Aucune demande récente
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
