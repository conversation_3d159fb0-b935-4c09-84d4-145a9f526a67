# 🤖 Guide d'intégration IA gratuite (Groq) pour la génération d'attestations

## 📋 Vue d'ensemble

Votre système RH dispose maintenant d'une intégration complète avec l'API Groq **100% gratuite** pour générer automatiquement des attestations de travail et de stage professionnelles et personnalisées.

## ✨ Pourquoi Groq ?

### 🆓 **Complètement gratuit**
- Aucun coût, aucune limite de crédits
- Pas de carte de crédit requise
- Utilisation illimitée

### ⚡ **Ultra-rapide**
- Génération en moins de 2 secondes
- Plus rapide qu'OpenAI
- Infrastructure optimisée

### 🧠 **Modèle avancé**
- Llama 3.1 8B Instant (très performant)
- Qualité professionnelle
- Compréhension du français excellente

## 🚀 Configuration Groq (5 minutes)

### 1. Obtenir une clé API Groq (Gratuite)

1. **Créer un compte Groq** :
   - Rendez-vous sur [console.groq.com](https://console.groq.com)
   - Créez un compte gratuit (email + mot de passe)

2. **Générer une clé API** :
   - Allez dans "API Keys" dans le menu
   - Cliquez sur "Create API Key"
   - Donnez un nom à votre clé (ex: "RH-Manager-Attestations")
   - Copiez la clé générée (elle commence par `gsk_`)

### 2. Configurer la clé API

1. **Modifier le fichier .env** :
   ```bash
   # Configuration IA gratuite - Groq
   GROQ_API_KEY="gsk_votre-cle-api-ici"
   ```

2. **Redémarrer le serveur** :
   ```bash
   npm run dev
   ```

3. **Vérifier la configuration** :
   - Allez dans "Configuration IA" dans le menu
   - Le statut devrait afficher "✅ IA GROQ connectée"

## 🎨 Fonctionnalités IA

### 🏢 **Attestation de travail**
L'IA génère automatiquement :
- Informations de l'employé (nom, prénom, poste)
- Date d'embauche et ancienneté
- Département et responsabilités
- Motif de la demande
- Format officiel français

### 🎓 **Attestation de stage**
L'IA génère automatiquement :
- Informations du stagiaire
- Dates de début et fin de stage
- Mission et département
- Établissement d'origine
- Évaluation du stage

### 🎯 **Modèles personnalisables**
- Templates prédéfinis pour chaque type
- Variables automatiquement remplies
- Adaptation selon le contexte
- Respect des normes administratives

## 💡 Utilisation

### Générer une attestation

1. **Connectez-vous** au système
2. **Allez dans "Attestations"** → Onglet "Générer"
3. **Sélectionnez une demande** d'attestation
4. **Choisissez le type** : Travail ou Stage
5. **Laissez vide** le contenu personnalisé (pour utiliser l'IA)
6. **Cliquez sur "Générer avec GROQ"**

### Résultat

L'IA génère une attestation comme :

```
ATTESTATION DE TRAVAIL

Je soussigné, Directeur des Ressources Humaines, certifie par la présente que :

Madame Sophie DUBOIS
Poste occupé : Développeuse Full Stack
Département : Informatique

est employée dans notre entreprise depuis le 01 juin 2021 en qualité de Développeuse Full Stack.

Durant cette période, l'intéressée a fait preuve de sérieux et de compétence dans l'exercice de ses fonctions.

Cette attestation est délivrée à l'intéressée pour servir et valoir ce que de droit.

Fait à Paris, le 15 janvier 2024

Le Directeur des Ressources Humaines
[Signature et cachet]
```

## 🔧 Fonctionnalités avancées

### ✅ **Statut en temps réel**
- Indicateur de connexion Groq
- Test de connexion disponible
- Messages d'erreur explicites

### 🎨 **Interface adaptative**
- Bouton coloré quand l'IA est active
- Messages d'aide contextuels
- Badge "IA" sur les attestations générées

### 🔄 **Fallback automatique**
- Si Groq n'est pas disponible, utilise un modèle de base
- Aucune interruption de service
- Notification claire du mode utilisé

## 🛠️ Dépannage

### ❌ "Clé API non configurée"
- Vérifiez que `GROQ_API_KEY` est dans .env
- Redémarrez le serveur après modification
- La clé doit commencer par `gsk_`

### ⚠️ "Connexion échouée"
- Vérifiez votre connexion internet
- Vérifiez que la clé API est valide
- Groq n'a pas de limite, donc pas de problème de quota

### 🔍 **Tests et diagnostic**

1. **Page Configuration IA** :
   - Statut de connexion en temps réel
   - Test de connexion manuel
   - Génération d'exemples

2. **Logs du serveur** :
   - Erreurs détaillées dans la console
   - Messages de debug pour l'API Groq

## 📈 Avantages de l'IA Groq

### Pour les RH
- **Gain de temps** : Génération instantanée (2 secondes)
- **Qualité** : Texte professionnel et adapté
- **Cohérence** : Format standardisé
- **Personnalisation** : Contenu adapté au contexte
- **Gratuit** : Aucun coût d'utilisation

### Pour l'entreprise
- **Efficacité** : Traitement rapide des demandes
- **Professionnalisme** : Documents de qualité
- **Économies** : Pas de coûts d'IA
- **Évolutivité** : Facilement extensible

## 🆚 Comparaison avec OpenAI

| Critère | Groq | OpenAI |
|---------|------|--------|
| **Coût** | 🟢 100% Gratuit | 🔴 Payant après crédits |
| **Vitesse** | 🟢 Ultra-rapide (2s) | 🟡 Rapide (5-10s) |
| **Limites** | 🟢 Aucune limite | 🔴 Limites de taux |
| **Qualité** | 🟢 Excellente | 🟢 Excellente |
| **Configuration** | 🟢 Simple | 🟡 Plus complexe |

## 🔮 Évolutions futures

### Fonctionnalités prévues
- **Modèles personnalisés** : Templates spécifiques à l'entreprise
- **Langues multiples** : Génération en plusieurs langues
- **Signatures automatiques** : Intégration de signatures numériques
- **Export PDF** : Génération directe en PDF
- **Envoi automatique** : Email automatique aux employés

## 📞 Support

### En cas de problème
1. Vérifiez ce guide
2. Consultez la page "Configuration IA"
3. Vérifiez les logs du serveur
4. Testez avec les exemples fournis

### Ressources
- [Documentation Groq](https://console.groq.com/docs)
- [Console Groq](https://console.groq.com)
- [Statut des services Groq](https://status.groq.com)

---

🎉 **Félicitations !** Votre système RH dispose maintenant d'une IA gratuite et ultra-performante pour la génération d'attestations. L'intégration Groq offre une qualité professionnelle sans aucun coût !
