-- Script SQL pour insérer des données de test
-- À exécuter après create-tables.sql dans l'éditeur SQL de Supabase

-- Insertion des utilisateurs
INSERT INTO "users" ("id", "email", "nom", "prenom", "poste", "departement", "manager", "telephone", "dateEmbauche", "salaire", "congesRestants", "role", "createdAt", "updatedAt") VALUES
('user1', '<EMAIL>', '<PERSON><PERSON>', '<PERSON>', 'Développeuse Senior', 'IT', '<PERSON>', '+33 1 23 45 67 89', '2020-03-15T00:00:00.000Z', 55000, 25, 'EMPLOYE', NOW(), NOW()),
('user2', '<EMAIL>', 'Moreau', '<PERSON>', 'Comptable', 'Finance', '<PERSON>', '+33 1 23 45 67 90', '2019-09-01T00:00:00.000Z', 42000, 18, 'EMPLOYE', NOW(), NOW()),
('user3', '<EMAIL>', '<PERSON>', '<PERSON>', 'Responsable RH', 'RH', 'Directeur Général', '+33 1 23 45 67 91', '2018-01-10T00:00:00.000Z', 65000, 30, 'RH', NOW(), NOW()),
('user4', '<EMAIL>', 'Admin', 'Système', 'Administrateur', 'IT', NULL, NULL, NULL, NULL, 25, 'ADMIN', NOW(), NOW()),
('user5', '<EMAIL>', 'Petit', 'Thomas', 'Chef de projet', 'Marketing', 'Claire Durand', '+33 1 23 45 67 92', '2021-06-01T00:00:00.000Z', 48000, 22, 'EMPLOYE', NOW(), NOW());

-- Insertion des demandes
INSERT INTO "demandes" ("id", "type", "statut", "priorite", "titre", "description", "motif", "dateDebut", "dateFin", "userId", "createdAt", "updatedAt") VALUES
('demande1', 'CONGE', 'EN_ATTENTE', 'NORMALE', 'Demande de congés annuels', 'Demande de congés pour vacances familiales', 'Congés annuels', '2024-02-01T00:00:00.000Z', '2024-02-15T00:00:00.000Z', 'user1', NOW(), NOW()),
('demande3', 'CONGE', 'REJETEE', 'NORMALE', 'Congés maladie', 'Demande de congés pour raisons médicales', 'Congés maladie', '2024-01-20T00:00:00.000Z', '2024-01-25T00:00:00.000Z', 'user5', NOW(), NOW());

INSERT INTO "demandes" ("id", "type", "statut", "priorite", "titre", "description", "materiel", "quantite", "budgetEstime", "justification", "dateTraitement", "userId", "createdAt", "updatedAt") VALUES
('demande2', 'MATERIEL', 'VALIDEE', 'URGENTE', 'Demande ordinateur portable', 'Remplacement ordinateur défaillant', 'Ordinateur portable', 1, 1200, 'Ordinateur actuel en panne', '2024-01-12T00:00:00.000Z', 'user2', NOW(), NOW());

INSERT INTO "demandes" ("id", "type", "statut", "priorite", "titre", "description", "typeAttestation", "motifAttestation", "userId", "createdAt", "updatedAt") VALUES
('demande4', 'ATTESTATION', 'EN_COURS', 'NORMALE', 'Attestation de travail', 'Demande attestation pour prêt bancaire', 'travail', 'Demande de prêt bancaire', 'user3', NOW(), NOW()),
('demande5', 'ATTESTATION', 'EN_ATTENTE', 'NORMALE', 'Attestation de salaire', 'Attestation pour dossier logement', 'salaire', 'Dossier de location', 'user1', NOW(), NOW());

-- Insertion d'une attestation
INSERT INTO "attestations" ("id", "contenu", "statut", "genereParIA", "demandeId", "createdAt", "updatedAt") VALUES
('attestation1', 'Je soussigné, Directeur des Ressources Humaines de l''entreprise RH Manager, certifie par la présente que :

Madame Sophie Bernard,
Poste occupé : Responsable RH
Département : RH

est employée dans notre entreprise depuis le 10 janvier 2018 en qualité de Responsable RH. Cette attestation est délivrée pour servir et valoir ce que de droit.

Fait à Paris, le ' || TO_CHAR(NOW(), 'DD/MM/YYYY') || '

Signature et cachet de l''entreprise
Directeur des Ressources Humaines', 'GENEREE', true, 'demande4', NOW(), NOW());

-- Insertion des templates d'attestations
INSERT INTO "templates_attestations" ("id", "nom", "type", "contenu", "createdAt", "updatedAt") VALUES
('template1', 'Attestation de travail standard', 'travail', 'Je soussigné, {{directeur_rh}}, certifie par la présente que :

{{civilite}} {{nom}} {{prenom}},
Poste occupé : {{poste}}
Département : {{departement}}

est employé(e) dans notre entreprise depuis le {{date_embauche}} en qualité de {{poste}}. Cette attestation est délivrée pour {{motif}}.

Fait à {{ville}}, le {{date}}

{{signature}}', NOW(), NOW()),
('template2', 'Attestation de salaire', 'salaire', 'Je soussigné, {{directeur_rh}}, certifie par la présente que :

{{civilite}} {{nom}} {{prenom}},
Poste occupé : {{poste}}

perçoit un salaire mensuel brut de {{salaire}} euros. Cette attestation est délivrée à sa demande pour {{motif}}.

Fait à {{ville}}, le {{date}}

{{signature}}', NOW(), NOW());

-- Insertion des paramètres système
INSERT INTO "parametres" ("id", "cle", "valeur", "description", "type", "createdAt", "updatedAt") VALUES
('param1', 'nom_entreprise', 'RH Manager', 'Nom de l''entreprise', 'TEXTE', NOW(), NOW()),
('param2', 'adresse_entreprise', '123 Rue de la Paix, 75001 Paris', 'Adresse de l''entreprise', 'TEXTE', NOW(), NOW()),
('param3', 'email_entreprise', '<EMAIL>', 'Email de contact de l''entreprise', 'EMAIL', NOW(), NOW()),
('param4', 'directeur_rh', 'Sophie Bernard', 'Nom du directeur RH', 'TEXTE', NOW(), NOW()),
('param5', 'max_conges_consecutifs', '30', 'Nombre maximum de jours de congés consécutifs', 'NOMBRE', NOW(), NOW()),
('param6', 'auto_approve_conges', 'false', 'Validation automatique des congés', 'BOOLEEN', NOW(), NOW());

-- Insertion des notifications
INSERT INTO "notifications" ("id", "titre", "message", "type", "userId", "createdAt") VALUES
('notif1', 'Nouvelle demande de congé', 'Marie Dubois a soumis une demande de congé du 01/02/2024 au 15/02/2024', 'DEMANDE', 'user3', NOW()),
('notif2', 'Demande approuvée', 'Votre demande de matériel a été approuvée', 'VALIDATION', 'user2', NOW()),
('notif3', 'Attestation générée', 'Votre attestation de travail est prête', 'INFO', 'user3', NOW());
