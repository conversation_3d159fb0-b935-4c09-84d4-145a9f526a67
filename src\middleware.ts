import { auth } from "@/lib/auth"
import { NextResponse } from "next/server"

// Routes publiques qui ne nécessitent pas d'authentification
const publicRoutes = [
  "/",
  "/auth/signin",
  "/auth/error",
  "/auth/signup",
]

// Routes d'API publiques
const publicApiRoutes = [
  "/api/auth",
  "/api/health",
]

export default auth((req) => {
  const { nextUrl } = req
  const isLoggedIn = !!req.auth

  console.log(`🔍 Middleware - Route: ${nextUrl.pathname}, Connecté: ${isLoggedIn}`)

  // Vérifier si c'est une route API publique
  const isPublicApiRoute = publicApiRoutes.some(route =>
    nextUrl.pathname.startsWith(route)
  )

  // Vérifier si la route est publique
  const isPublicRoute = publicRoutes.includes(nextUrl.pathname) || isPublicApiRoute

  // Si l'utilisateur n'est pas connecté et essaie d'accéder à une route protégée
  if (!isLoggedIn && !isPublicRoute) {
    console.log(`🔒 Redirection vers signin depuis: ${nextUrl.pathname}`)
    const signInUrl = new URL("/auth/signin", nextUrl)
    signInUrl.searchParams.set("callbackUrl", nextUrl.pathname)
    return NextResponse.redirect(signInUrl)
  }

  // Si l'utilisateur est connecté et essaie d'accéder à la page de connexion
  if (isLoggedIn && nextUrl.pathname === "/auth/signin") {
    console.log("✅ Utilisateur connecté, redirection vers dashboard")
    return NextResponse.redirect(new URL("/dashboard", nextUrl))
  }

  // Protection spéciale pour la gestion des rôles (ADMIN seulement)
  if (isLoggedIn && nextUrl.pathname === "/dashboard/gestion-roles") {
    if (req.auth?.user?.role !== "ADMIN") {
      console.log(`❌ Accès refusé à gestion-roles pour: ${req.auth?.user?.role}`)
      return NextResponse.redirect(new URL("/dashboard", nextUrl))
    }
  }

  // Redirection de la racine vers le dashboard si connecté
  if (isLoggedIn && nextUrl.pathname === "/") {
    return NextResponse.redirect(new URL("/dashboard", nextUrl))
  }

  return NextResponse.next()
})

export const config = {
  matcher: [
    "/((?!api/auth|_next/static|_next/image|favicon.ico).*)",
  ],
}
