[{"id": 1, "employe": "<PERSON>", "email": "<EMAIL>", "departement": "IT", "poste": "Développeuse Senior", "type": "conge", "statut": "en_attente", "date_demande": "2024-01-15", "priorite": "normale", "manager": "<PERSON>"}, {"id": 2, "employe": "<PERSON>", "email": "<EMAIL>", "departement": "Finance", "poste": "Comptable", "type": "materiel", "statut": "validee", "date_demande": "2024-01-10", "priorite": "urgente", "manager": "<PERSON>"}, {"id": 3, "employe": "<PERSON>", "email": "<EMAIL>", "departement": "RH", "poste": "Responsable RH", "type": "attestation", "statut": "en_cours", "date_demande": "2024-01-14", "priorite": "normale", "manager": "Directeur <PERSON>"}, {"id": 4, "employe": "<PERSON>", "email": "<EMAIL>", "departement": "Marketing", "poste": "Chef de projet", "type": "conge", "statut": "rejetee", "date_demande": "2024-01-08", "priorite": "normale", "manager": "<PERSON>"}, {"id": 5, "employe": "<PERSON>", "email": "<EMAIL>", "departement": "IT", "poste": "<PERSON><PERSON><PERSON> sys<PERSON>", "type": "materiel", "statut": "en_attente", "date_demande": "2024-01-16", "priorite": "normale", "manager": "<PERSON>"}, {"id": 6, "employe": "<PERSON>", "email": "<EMAIL>", "departement": "Finance", "poste": "<PERSON><PERSON>ste financier", "type": "attestation", "statut": "validee", "date_demande": "2024-01-12", "priorite": "urgente", "manager": "<PERSON>"}, {"id": 7, "employe": "<PERSON>", "email": "<EMAIL>", "departement": "Marketing", "poste": "Responsable communication", "type": "conge", "statut": "validee", "date_demande": "2024-01-11", "priorite": "normale", "manager": "<PERSON>"}, {"id": 8, "employe": "<PERSON>", "email": "<EMAIL>", "departement": "IT", "poste": "DevOps Engineer", "type": "materiel", "statut": "en_cours", "date_demande": "2024-01-17", "priorite": "normale", "manager": "<PERSON>"}, {"id": 9, "employe": "<PERSON>", "email": "<EMAIL>", "departement": "RH", "poste": "<PERSON><PERSON>", "type": "attestation", "statut": "en_attente", "date_demande": "2024-01-18", "priorite": "normale", "manager": "<PERSON>"}, {"id": 10, "employe": "<PERSON><PERSON>", "email": "<EMAIL>", "departement": "Finance", "poste": "Contrôleur de gestion", "type": "conge", "statut": "en_attente", "date_demande": "2024-01-19", "priorite": "normale", "manager": "<PERSON>"}]