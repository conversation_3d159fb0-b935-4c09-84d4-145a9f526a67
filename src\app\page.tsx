'use client';

import { useState } from 'react';
import Link from 'next/link';
import {
  FileText, Calendar, Shield, Users, CheckCircle, Clock, ArrowRight,
  Sparkles, Star, Play, ChevronDown, Award, TrendingUp, Zap,
  MessageSquare, BarChart3, Globe, Smartphone, Lock,
  CheckCircle2, ArrowUpRight, Menu, X
} from "lucide-react";

export default function HomePage() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeTestimonial, setActiveTestimonial] = useState(0);

  const testimonials = [
    {
      name: "<PERSON>",
      role: "Directrice RH, TechCorp",
      content: "SGDI a transformé notre gestion des demandes. Nous avons réduit le temps de traitement de 60% et nos employés sont plus satisfaits.",
      rating: 5,
      avatar: "SD"
    },
    {
      name: "<PERSON>",
      role: "Responsable RH, InnovatePlus",
      content: "L'interface est intuitive et le suivi en temps réel nous permet d'être plus réactifs. Un outil pratique pour notre équipe.",
      rating: 5,
      avatar: "ML"
    },
    {
      name: "<PERSON>",
      role: "Gestionnaire RH, StartupTech",
      content: "Une solution qui simplifie vraiment les démarches administratives. Nos employés apprécient la facilité d'utilisation.",
      rating: 5,
      avatar: "JM"
    }
  ];

  const stats = [
    { number: "2K+", label: "Employés actifs", description: "Utilisateurs quotidiens" },
    { number: "50+", label: "Entreprises", description: "Nous font confiance" },
    { number: "95%", label: "Satisfaction", description: "Taux de satisfaction" },
    { number: "24h", label: "Temps de traitement", description: "Délai moyen" }
  ];

  const benefits = [
    {
      icon: Clock,
      title: "Gain de temps",
      description: "Réduisez de 70% le temps consacré aux tâches administratives",
      metric: "70%"
    },
    {
      icon: Users,
      title: "Satisfaction employés",
      description: "Améliorez l'expérience de vos collaborateurs",
      metric: "95%"
    },
    {
      icon: TrendingUp,
      title: "Productivité",
      description: "Augmentez l'efficacité de votre équipe RH",
      metric: "+40%"
    }
  ];

  const features = [
    {
      icon: FileText,
      title: "Demandes d'attestations",
      description: "Créez et suivez vos demandes d'attestations de travail, de salaire et autres documents officiels en quelques clics.",
      details: ["Génération automatique", "Modèles personnalisables", "Suivi en temps réel"]
    },
    {
      icon: Calendar,
      title: "Gestion des congés",
      description: "Planifiez et gérez tous types de congés avec un calendrier intelligent et des processus automatisés.",
      details: ["Congés maternité/paternité", "Congés payés", "Arrêts maladie"]
    },
    {
      icon: Shield,
      title: "Sécurité avancée",
      description: "Protection des données avec chiffrement et conformité RGPD garantie pour vos informations sensibles.",
      details: ["Chiffrement sécurisé", "Authentification forte", "Audit complet"]
    },
    {
      icon: BarChart3,
      title: "Tableaux de bord",
      description: "Visualisez vos données RH avec des tableaux de bord personnalisés pour optimiser vos processus.",
      details: ["Rapports automatisés", "Métriques en temps réel", "Export de données"]
    },
    {
      icon: Smartphone,
      title: "Interface moderne",
      description: "Accédez à toutes les fonctionnalités depuis n'importe quel appareil avec notre interface responsive.",
      details: ["Design adaptatif", "Navigation intuitive", "Accès mobile"]
    },
    {
      icon: Globe,
      title: "Support complet",
      description: "Bénéficiez d'un support technique et d'une documentation complète pour utiliser la plateforme.",
      details: ["Documentation détaillée", "Support technique", "Formation utilisateurs"]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-gray-100">
      {/* Header avec navigation */}
      <header className="fixed top-0 w-full z-50 bg-white/95 backdrop-blur-lg border-b border-slate-200/60 shadow-sm">
        <div className="container mx-auto px-6 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-br from-slate-700 to-slate-900 rounded-lg flex items-center justify-center">
                  <FileText className="w-4 h-4 text-white" />
                </div>
                <span className="text-xl font-bold text-slate-800">
                  SGDI
                </span>
              </div>
              <span className="hidden sm:inline text-sm text-slate-600 font-medium">
                Système de Gestion des Demandes
              </span>
            </div>

            {/* Navigation desktop */}
            <nav className="hidden md:flex items-center space-x-8">
              <a href="#features" className="text-slate-600 hover:text-slate-900 font-medium transition-colors">Fonctionnalités</a>
              <a href="#testimonials" className="text-slate-600 hover:text-slate-900 font-medium transition-colors">Témoignages</a>
              <a href="#contact" className="text-slate-600 hover:text-slate-900 font-medium transition-colors">Contact</a>
            </nav>

            <div className="flex items-center space-x-4">
              <Link href="/auth/signin" className="hidden md:inline-flex text-slate-600 hover:text-slate-900 font-medium transition-colors">
                Se connecter
              </Link>
              <Link href="/auth/signin" className="bg-gradient-to-r from-slate-800 to-slate-700 text-white px-6 py-2.5 rounded-lg hover:from-slate-900 hover:to-slate-800 transition-all duration-300 transform hover:scale-105 shadow-lg font-medium">
                Commencer
              </Link>

              {/* Menu mobile */}
              <button
                className="md:hidden p-2 rounded-lg hover:bg-slate-100 transition-colors"
                onClick={() => setIsMenuOpen(!isMenuOpen)}
              >
                {isMenuOpen ? <X className="w-5 h-5 text-slate-600" /> : <Menu className="w-5 h-5 text-slate-600" />}
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Menu mobile */}
      {isMenuOpen && (
        <div className="fixed inset-0 z-40 bg-white/95 backdrop-blur-lg pt-20 md:hidden">
          <nav className="flex flex-col space-y-6 p-6">
            <a href="#features" className="text-lg text-slate-700 font-medium">Fonctionnalités</a>
            <a href="#testimonials" className="text-lg text-slate-700 font-medium">Témoignages</a>
            <a href="#contact" className="text-lg text-slate-700 font-medium">Contact</a>
            <hr className="border-slate-200" />
            <Link href="/auth/signin" className="text-lg text-slate-700 font-medium">Se connecter</Link>
          </nav>
        </div>
      )}

      {/* Section Hero */}
      <section className="pt-32 pb-24 bg-gradient-to-br from-white via-slate-50 to-blue-50/30 relative overflow-hidden">
        <div className="absolute inset-0 opacity-40">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, rgb(148 163 184 / 0.15) 1px, transparent 0)`,
            backgroundSize: '20px 20px'
          }}></div>
        </div>
        <div className="container mx-auto px-6 relative">
          <div className="text-center max-w-5xl mx-auto">
            <div className="inline-flex items-center space-x-3 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-full px-6 py-3 mb-8 shadow-sm">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-semibold text-slate-700">Nouveau : IA intégrée pour l'automatisation</span>
            </div>

            <h1 className="text-4xl md:text-6xl font-bold text-slate-900 mb-8 leading-tight">
              Simplifiez votre gestion
              <span className="block bg-gradient-to-r from-blue-600 via-indigo-600 to-slate-800 bg-clip-text text-transparent">
                des demandes RH
              </span>
            </h1>

            <p className="text-lg md:text-xl text-slate-600 mb-12 max-w-3xl mx-auto leading-relaxed font-medium">
              SGDI vous aide à gérer efficacement les demandes d'attestations, congés et autres
              démarches administratives de vos employés avec une interface moderne et intuitive.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
              <Link href="/auth/signin" className="group bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl inline-flex items-center justify-center">
                <div className="flex items-center">
                  Commencer maintenant
                  <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                </div>
              </Link>
              <button className="group flex items-center justify-center border-2 border-slate-300 text-slate-700 px-8 py-4 rounded-xl text-lg font-semibold hover:border-slate-400 hover:bg-slate-50 transition-all duration-300 bg-white/80 backdrop-blur-sm">
                <Play className="w-5 h-5 mr-2" />
                Voir la démo
              </button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-5xl mx-auto">
              {stats.map((stat, index) => (
                <div key={index} className="group text-center p-6 bg-white/70 backdrop-blur-sm rounded-2xl border border-slate-200/50 shadow-lg hover:shadow-xl transition-all duration-500 hover:-translate-y-2 hover:bg-white/90">
                  <div className="text-2xl md:text-3xl font-bold text-slate-900 mb-2 group-hover:scale-110 transition-transform duration-300">{stat.number}</div>
                  <div className="text-slate-700 text-sm font-semibold mb-1">{stat.label}</div>
                  <div className="text-slate-500 text-xs font-medium">{stat.description}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Section Avantages */}
      <section className="py-20 bg-white relative overflow-hidden">
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500"></div>
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-6">
              Pourquoi choisir SGDI ?
            </h2>
            <p className="text-lg text-slate-600 max-w-3xl mx-auto font-medium">
              Découvrez les avantages concrets que SGDI apporte à votre organisation
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {benefits.map((benefit, index) => (
              <div key={index} className="group text-center p-8 rounded-3xl bg-gradient-to-br from-slate-50 to-blue-50/30 border border-slate-200/60 hover:shadow-2xl transition-all duration-500 hover:-translate-y-3">
                <div className="w-20 h-20 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                  <benefit.icon className="w-10 h-10 text-white" />
                </div>
                <div className="text-4xl font-bold text-blue-600 mb-4 group-hover:scale-105 transition-transform duration-300">
                  {benefit.metric}
                </div>
                <h3 className="text-xl font-bold text-slate-900 mb-4">{benefit.title}</h3>
                <p className="text-slate-600 font-medium leading-relaxed">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Section Fonctionnalités */}
      <section id="features" className="py-24 bg-gradient-to-b from-white to-slate-50">
        <div className="container mx-auto px-6">
          <div className="text-center mb-20">
            <h2 className="text-3xl md:text-5xl font-bold text-slate-900 mb-6">
              Fonctionnalités principales
            </h2>
            <p className="text-lg md:text-xl text-slate-600 max-w-3xl mx-auto font-medium">
              Découvrez comment SGDI simplifie la gestion de vos demandes RH avec des outils
              modernes et une interface intuitive.
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {features.map((feature, index) => (
              <div key={index} className="group relative bg-white rounded-3xl p-8 border border-slate-200/60 hover:border-blue-200 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-indigo-50/50 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <feature.icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 mb-4">{feature.title}</h3>
                  <p className="text-slate-600 mb-6 leading-relaxed font-medium">{feature.description}</p>
                  <ul className="space-y-3">
                    {feature.details.map((detail, idx) => (
                      <li key={idx} className="flex items-center text-sm text-slate-600 font-medium">
                        <CheckCircle2 className="w-4 h-4 text-blue-500 mr-3 flex-shrink-0" />
                        {detail}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Section Comment ça marche */}
      <section className="py-24 bg-gradient-to-br from-blue-50 to-indigo-50 relative overflow-hidden">
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0" style={{
            backgroundImage: `linear-gradient(45deg, rgb(59 130 246 / 0.1) 25%, transparent 25%), linear-gradient(-45deg, rgb(59 130 246 / 0.1) 25%, transparent 25%), linear-gradient(45deg, transparent 75%, rgb(59 130 246 / 0.1) 75%), linear-gradient(-45deg, transparent 75%, rgb(59 130 246 / 0.1) 75%)`,
            backgroundSize: '20px 20px',
            backgroundPosition: '0 0, 0 10px, 10px -10px, -10px 0px'
          }}></div>
        </div>

        <div className="container mx-auto px-6 relative">
          <div className="text-center mb-20">
            <h2 className="text-3xl md:text-5xl font-bold text-slate-900 mb-6">
              Comment ça marche ?
            </h2>
            <p className="text-lg md:text-xl text-slate-600 max-w-3xl mx-auto font-medium">
              Découvrez en 3 étapes simples comment SGDI transforme votre gestion RH
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-12 max-w-6xl mx-auto">
            {[
              {
                step: "01",
                title: "Inscription rapide",
                description: "Créez votre compte en moins de 2 minutes et configurez votre espace de travail selon vos besoins.",
                icon: Users,
                color: "from-blue-500 to-blue-600"
              },
              {
                step: "02",
                title: "Configuration simple",
                description: "Paramétrez vos types de demandes, vos workflows et invitez votre équipe en quelques clics.",
                icon: Shield,
                color: "from-indigo-500 to-indigo-600"
              },
              {
                step: "03",
                title: "Gestion automatisée",
                description: "Laissez SGDI gérer vos demandes automatiquement avec l'IA et profitez du gain de temps.",
                icon: Zap,
                color: "from-purple-500 to-purple-600"
              }
            ].map((item, index) => (
              <div key={index} className="group relative">
                {/* Ligne de connexion */}
                {index < 2 && (
                  <div className="hidden md:block absolute top-16 left-full w-12 h-0.5 bg-gradient-to-r from-slate-300 to-slate-400 z-10 transform translate-x-6">
                    <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-slate-400 rounded-full"></div>
                  </div>
                )}

                <div className="text-center p-8 bg-white/80 backdrop-blur-sm rounded-3xl border border-slate-200/60 shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 group-hover:bg-white">
                  {/* Numéro d'étape */}
                  <div className="relative mb-6">
                    <div className={`w-20 h-20 bg-gradient-to-br ${item.color} rounded-full flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                      <item.icon className="w-10 h-10 text-white" />
                    </div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-slate-900 text-white rounded-full flex items-center justify-center text-sm font-bold">
                      {item.step}
                    </div>
                  </div>

                  <h3 className="text-xl font-bold text-slate-900 mb-4">{item.title}</h3>
                  <p className="text-slate-600 font-medium leading-relaxed">{item.description}</p>
                </div>
              </div>
            ))}
          </div>

          {/* CTA Section */}
          <div className="text-center mt-16">
            <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-8 border border-slate-200/60 shadow-lg max-w-2xl mx-auto">
              <h3 className="text-2xl font-bold text-slate-900 mb-4">
                Prêt à commencer ?
              </h3>
              <p className="text-slate-600 mb-6 font-medium">
                Rejoignez les entreprises qui ont déjà simplifié leur gestion RH
              </p>
              <Link href="/auth/signin" className="inline-flex items-center bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 shadow-xl">
                Démarrer gratuitement
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Section Témoignages */}
      <section id="testimonials" className="py-24 bg-gradient-to-br from-slate-100 to-blue-50/30">
        <div className="container mx-auto px-6">
          <div className="text-center mb-20">
            <h2 className="text-3xl md:text-5xl font-bold text-slate-900 mb-6">
              Ils nous font confiance
            </h2>
            <p className="text-lg md:text-xl text-slate-600 max-w-3xl mx-auto font-medium">
              Découvrez comment SGDI améliore le quotidien des équipes RH
              dans différentes entreprises.
            </p>
          </div>

          <div className="max-w-5xl mx-auto">
            <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 md:p-12 shadow-2xl border border-slate-200/60">
              <div className="text-center mb-8">
                <div className="flex justify-center mb-6">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-6 h-6 text-amber-400 fill-current" />
                  ))}
                </div>
                <blockquote className="text-lg md:text-2xl text-slate-700 leading-relaxed mb-8 font-medium italic">
                  "{testimonials[activeTestimonial].content}"
                </blockquote>
                <div className="flex items-center justify-center space-x-4">
                  <div className="w-14 h-14 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg">
                    {testimonials[activeTestimonial].avatar}
                  </div>
                  <div className="text-left">
                    <div className="font-bold text-slate-900 text-lg">{testimonials[activeTestimonial].name}</div>
                    <div className="text-slate-600 font-medium">{testimonials[activeTestimonial].role}</div>
                  </div>
                </div>
              </div>

              <div className="flex justify-center space-x-3">
                {testimonials.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setActiveTestimonial(index)}
                    className={`h-3 rounded-full transition-all duration-300 ${
                      index === activeTestimonial
                        ? 'bg-blue-600 w-8'
                        : 'bg-slate-300 hover:bg-slate-400 w-3'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Section CTA finale */}
      <section className="py-24 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, rgb(255 255 255 / 0.15) 1px, transparent 0)`,
            backgroundSize: '30px 30px'
          }}></div>
        </div>
        <div className="container mx-auto px-6 text-center relative">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl md:text-5xl font-bold text-white mb-6">
              Prêt à simplifier votre gestion RH ?
            </h2>
            <p className="text-lg md:text-xl text-blue-100 mb-12 max-w-2xl mx-auto font-medium">
              Rejoignez les entreprises qui ont déjà amélioré
              leur gestion des demandes avec SGDI.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <Link href="/auth/signin" className="bg-white text-slate-900 px-8 py-4 rounded-xl text-lg font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-xl inline-flex items-center justify-center">
                Commencer maintenant
              </Link>
              <button className="border-2 border-white/30 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:bg-white/10 hover:border-white/50 transition-all duration-300 backdrop-blur-sm">
                En savoir plus
              </button>
            </div>

            <div className="flex flex-wrap justify-center items-center gap-8 text-blue-100">
              <div className="flex items-center font-medium">
                <CheckCircle2 className="w-5 h-5 mr-2" />
                Gratuit pour commencer
              </div>
              <div className="flex items-center font-medium">
                <CheckCircle2 className="w-5 h-5 mr-2" />
                Support inclus
              </div>
              <div className="flex items-center font-medium">
                <CheckCircle2 className="w-5 h-5 mr-2" />
                Configuration simple
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden">
        {/* Motif de fond animé */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 2px 2px, rgb(59 130 246 / 0.3) 1px, transparent 0)`,
            backgroundSize: '40px 40px'
          }}></div>
        </div>

        {/* Gradient animé en haut */}
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 animate-pulse"></div>

        <div className="container mx-auto px-6 py-20 relative">
          {/* Section principale */}
          <div className="grid lg:grid-cols-5 gap-12 mb-16">
            {/* Colonne principale - Logo et description */}
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-3 mb-6 group">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                  <FileText className="w-6 h-6 text-white" />
                </div>
                <span className="text-2xl font-bold text-white">
                  SGDI
                </span>
              </div>
              <p className="text-slate-300 leading-relaxed font-medium mb-6 text-lg">
                Système de Gestion des Demandes - La solution moderne
                pour simplifier et automatiser vos processus RH.
              </p>

              {/* Statistiques rapides */}
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="bg-slate-800/50 rounded-lg p-4 border border-slate-700/50 hover:border-blue-500/50 transition-colors duration-300">
                  <div className="text-2xl font-bold text-blue-400">2K+</div>
                  <div className="text-slate-400 text-sm">Utilisateurs actifs</div>
                </div>
                <div className="bg-slate-800/50 rounded-lg p-4 border border-slate-700/50 hover:border-blue-500/50 transition-colors duration-300">
                  <div className="text-2xl font-bold text-blue-400">50+</div>
                  <div className="text-slate-400 text-sm">Entreprises</div>
                </div>
              </div>

              {/* Bouton CTA */}
              <Link href="/auth/signin" className="inline-flex items-center bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 shadow-lg">
                Commencer gratuitement
                <ArrowRight className="w-4 h-4 ml-2" />
              </Link>
            </div>

            {/* Liens de navigation */}
            <div>
              <h3 className="font-bold mb-6 text-white text-lg">Produit</h3>
              <ul className="space-y-4 text-slate-300">
                <li><a href="#features" className="hover:text-blue-400 transition-colors font-medium flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  Fonctionnalités
                </a></li>
                <li><Link href="/auth/signin" className="hover:text-blue-400 transition-colors font-medium flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  Se connecter
                </Link></li>
                <li><a href="#" className="hover:text-blue-400 transition-colors font-medium flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  Documentation
                </a></li>
                <li><a href="#" className="hover:text-blue-400 transition-colors font-medium flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  API
                </a></li>
              </ul>
            </div>

            <div>
              <h3 className="font-bold mb-6 text-white text-lg">Entreprise</h3>
              <ul className="space-y-4 text-slate-300">
                <li><a href="#" className="hover:text-blue-400 transition-colors font-medium flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  À propos
                </a></li>
                <li><a href="#" className="hover:text-blue-400 transition-colors font-medium flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  Blog
                </a></li>
                <li><a href="#" className="hover:text-blue-400 transition-colors font-medium flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  Carrières
                </a></li>
                <li><a href="#contact" className="hover:text-blue-400 transition-colors font-medium flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  Contact
                </a></li>
              </ul>
            </div>

            <div>
              <h3 className="font-bold mb-6 text-white text-lg">Support</h3>
              <ul className="space-y-4 text-slate-300">
                <li><a href="#" className="hover:text-blue-400 transition-colors font-medium flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  Centre d'aide
                </a></li>
                <li><a href="#" className="hover:text-blue-400 transition-colors font-medium flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  Guides
                </a></li>
                <li><a href="#" className="hover:text-blue-400 transition-colors font-medium flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  FAQ
                </a></li>
                <li><a href="#" className="hover:text-blue-400 transition-colors font-medium flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  Sécurité
                </a></li>
              </ul>
            </div>
          </div>

          {/* Newsletter */}
          <div className="bg-gradient-to-r from-blue-900/30 to-indigo-900/30 rounded-2xl p-8 mb-12 border border-slate-700/50 backdrop-blur-sm">
            <div className="text-center max-w-2xl mx-auto">
              <h3 className="text-2xl font-bold text-white mb-4">Restez informé</h3>
              <p className="text-slate-300 mb-6 font-medium">
                Recevez les dernières actualités et mises à jour de SGDI directement dans votre boîte mail.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                <input
                  type="email"
                  placeholder="Votre adresse email"
                  className="flex-1 px-4 py-3 rounded-lg bg-slate-800/50 border border-slate-600 text-white placeholder-slate-400 focus:outline-none focus:border-blue-500 transition-colors duration-300"
                />
                <button className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105">
                  S'abonner
                </button>
              </div>
            </div>
          </div>

          {/* Ligne de séparation avec animation */}
          <div className="relative mb-8">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-slate-700"></div>
            </div>
            <div className="relative flex justify-center">
              <div className="bg-slate-900 px-4">
                <div className="w-8 h-1 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full"></div>
              </div>
            </div>
          </div>

          {/* Footer bottom */}
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-4 mb-4 md:mb-0">
              <p className="text-slate-400 text-sm font-medium">
                © 2025 SGDI. Tous droits réservés.
              </p>
              <div className="hidden md:flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-slate-400 text-xs">Système opérationnel</span>
              </div>
            </div>

            <div className="flex flex-wrap justify-center md:justify-end items-center gap-6">
              <a href="#" className="text-slate-400 hover:text-blue-400 transition-colors text-sm font-medium">
                Politique de confidentialité
              </a>
              <a href="#" className="text-slate-400 hover:text-blue-400 transition-colors text-sm font-medium">
                Conditions d'utilisation
              </a>
              <a href="#" className="text-slate-400 hover:text-blue-400 transition-colors text-sm font-medium">
                RGPD
              </a>

              {/* Réseaux sociaux */}
              <div className="flex items-center space-x-3 ml-4">
                <a href="#" className="w-8 h-8 bg-slate-800 rounded-lg flex items-center justify-center hover:bg-blue-600 transition-colors duration-300 group">
                  <Globe className="w-4 h-4 text-slate-400 group-hover:text-white" />
                </a>
                <a href="#" className="w-8 h-8 bg-slate-800 rounded-lg flex items-center justify-center hover:bg-blue-600 transition-colors duration-300 group">
                  <MessageSquare className="w-4 h-4 text-slate-400 group-hover:text-white" />
                </a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}