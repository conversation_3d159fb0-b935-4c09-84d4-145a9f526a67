import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Début du seeding simple...')
  
  // Créer quelques utilisateurs de base
  console.log('👥 Création des utilisateurs...')
  
  const user1 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: '<PERSON>',
      role: 'user',
    },
  })

  const user2 = await prisma.user.create({
    data: {
      email: '<EMAIL>', 
      name: '<PERSON>',
      role: 'user',
    },
  })

  const user3 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: 'Admin',
      role: 'admin',
    },
  })

  console.log('📋 Création des demandes...')
  
  const demande1 = await prisma.demande.create({
    data: {
      type: 'CONGE',
      message: 'Demande de congés pour vacances',
      status: 'pending',
      userId: user1.id,
    },
  })

  const demande2 = await prisma.demande.create({
    data: {
      type: 'MATERIEL',
      message: 'Demande ordinateur portable',
      status: 'approved',
      userId: user2.id,
    },
  })

  const demande3 = await prisma.demande.create({
    data: {
      type: 'ATTESTATION',
      message: 'Demande attestation de travail',
      status: 'in_progress',
      userId: user1.id,
    },
  })

  console.log('📄 Création d\'une attestation...')
  
  await prisma.attestation.create({
    data: {
      contenu: 'Attestation de travail pour Marie Dubois...',
      demandeId: demande3.id,
    },
  })

  console.log('✅ Seeding terminé avec succès!')
  console.log(`📊 Créé: 3 utilisateurs, 3 demandes, 1 attestation`)
}

main()
  .catch((e) => {
    console.error('❌ Erreur lors du seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
    console.log('🔌 Connexion fermée')
  })
