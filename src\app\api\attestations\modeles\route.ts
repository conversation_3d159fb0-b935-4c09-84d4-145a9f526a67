import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { obtenirModelesDisponibles, MODELES_ATTESTATION } from '@/lib/groq-ai';

// GET - Récupérer les modèles d'attestation
export async function GET() {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Non authentifié' },
        { status: 401 }
      );
    }

    const modeles = obtenirModelesDisponibles();

    return NextResponse.json({
      success: true,
      modeles: {
        travail: {
          titre: modeles.travail.titre,
          template: modeles.travail.template,
          variables: modeles.travail.variables,
          description: "Modèle pour les attestations de travail des employés"
        },
        stage: {
          titre: modeles.stage.titre,
          template: modeles.stage.template,
          variables: modeles.stage.variables,
          description: "Modèle pour les attestations de stage des stagiaires"
        }
      }
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des modèles:', error);
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    );
  }
}

// POST - Prévisualiser une attestation avec un modèle
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Non authentifié' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { type, donnees, modelePersonnalise } = body;

    if (!['travail', 'stage'].includes(type)) {
      return NextResponse.json(
        { error: 'Type d\'attestation invalide' },
        { status: 400 }
      );
    }

    // Utiliser le modèle par défaut ou personnalisé
    const modele = modelePersonnalise || MODELES_ATTESTATION[type].template;
    const dateActuelle = new Date().toLocaleDateString('fr-FR');
    
    // Remplacer les variables de base
    let preview = modele
      .replace('{fonction_signataire}', MODELES_ATTESTATION[type].variables.fonction_signataire)
      .replace('{prenom}', donnees.prenom || '[Prénom]')
      .replace('{nom}', donnees.nom || '[Nom]')
      .replace('{lieu}', MODELES_ATTESTATION[type].variables.lieu)
      .replace('{date_actuelle}', dateActuelle)
      .replace('{signature_bloc}', MODELES_ATTESTATION[type].variables.signature_bloc)
      .replace('{motif}', donnees.motif || 'servir et valoir ce que de droit');

    // Variables spécifiques selon le type
    if (type === 'travail') {
      preview = preview
        .replace('{poste_info}', donnees.poste ? `Poste occupé : ${donnees.poste}` : '[Poste]')
        .replace('{departement_info}', donnees.departement ? `Département : ${donnees.departement}` : '[Département]')
        .replace('{statut_emploi}', 'est employé(e)')
        .replace('{date_embauche_info}', donnees.dateEmbauche ? `depuis le ${new Date(donnees.dateEmbauche).toLocaleDateString('fr-FR')}` : '[Date d\'embauche]')
        .replace('{poste_detail}', donnees.poste ? `en qualité de ${donnees.poste}` : '[Poste]')
        .replace('{performance_info}', 'Durant cette période, l\'intéressé(e) a fait preuve de sérieux et de compétence dans l\'exercice de ses fonctions.');
    } else {
      preview = preview
        .replace('{etablissement_info}', donnees.etablissement ? `Étudiant(e) à ${donnees.etablissement}` : '[Établissement]')
        .replace('{periode_stage}', 
          donnees.dateDebutStage && donnees.dateFinStage 
            ? `du ${new Date(donnees.dateDebutStage).toLocaleDateString('fr-FR')} au ${new Date(donnees.dateFinStage).toLocaleDateString('fr-FR')}`
            : '[Période de stage]'
        )
        .replace('{poste_detail}', donnees.poste ? `en qualité de ${donnees.poste}` : '[Mission de stage]')
        .replace('{departement_info}', donnees.departement ? `Au sein du département ${donnees.departement}.` : '[Département]')
        .replace('{evaluation_stage}', 'Le stage s\'est déroulé dans de bonnes conditions.')
        .replace('{competences_acquises}', 'L\'intéressé(e) a pu acquérir une expérience professionnelle enrichissante.');
    }

    // Nettoyer les lignes vides multiples
    preview = preview.replace(/\n\s*\n\s*\n/g, '\n\n');

    return NextResponse.json({
      success: true,
      preview: preview,
      type: type,
      variables_utilisees: Object.keys(MODELES_ATTESTATION[type].variables)
    });

  } catch (error) {
    console.error('Erreur lors de la prévisualisation:', error);
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    );
  }
}
