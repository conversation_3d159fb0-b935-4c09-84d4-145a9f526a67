import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Optimisations pour réduire l'utilisation d'espace disque
  experimental: {
    // Réduire la taille du cache
    isrMemoryCacheSize: 0,
  },

  // Optimiser les builds
  swcMinify: true,

  // Réduire la taille des bundles
  compiler: {
    removeConsole: process.env.NODE_ENV === "production",
  },

  // Configuration webpack pour optimiser l'espace
  webpack: (config, { dev }) => {
    if (!dev) {
      // En production, optimiser davantage
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          cacheGroups: {
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all',
            },
          },
        },
      };
    }
    return config;
  },
};

export default nextConfig;
