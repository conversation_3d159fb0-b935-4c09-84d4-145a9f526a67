"use client"

import { useState, useEffect } from "react"
import { signIn, getSession } from "next-auth/react"
import { useRouter, useSearchParams } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, Eye, EyeOff, Bot, Shield, Users } from "lucide-react"

export default function SignInPage() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState("")
  const router = useRouter()
  const searchParams = useSearchParams()

  // Vérifier si déjà connecté
  useEffect(() => {
    const checkSession = async () => {
      const session = await getSession()
      if (session) {
        router.push('/dashboard')
      }
    }
    checkSession()
  }, [router])

  // Gérer les erreurs d'URL
  useEffect(() => {
    const error = searchParams.get('error')
    if (error) {
      setError('Erreur de connexion. Veuillez réessayer.')
    }
  }, [searchParams])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    try {
      console.log("🔐 Tentative de connexion avec:", email)

      const result = await signIn("credentials", {
        email: email.toLowerCase().trim(),
        password,
        redirect: false,
      })

      console.log("📋 Résultat de connexion:", result)

      if (result?.error) {
        console.log("❌ Erreur de connexion:", result.error)
        setError("Email ou mot de passe incorrect")
      } else if (result?.ok) {
        console.log("✅ Connexion réussie, redirection...")
        // Attendre un peu pour que la session soit établie
        await new Promise(resolve => setTimeout(resolve, 500))
        router.push("/dashboard")
        router.refresh()
      } else {
        setError("Erreur de connexion inattendue")
      }
    } catch (error) {
      console.error("❌ Erreur lors de la connexion:", error)
      setError("Une erreur est survenue lors de la connexion")
    } finally {
      setIsLoading(false)
    }
  }



  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl grid md:grid-cols-2 gap-8 items-center">

        {/* Panneau de gauche - Informations */}
        <div className="hidden md:block space-y-6">
          <div className="text-center">
            <Bot className="h-16 w-16 text-blue-600 mx-auto mb-4" />
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              RH Manager IA
            </h1>
            <p className="text-gray-600">
              Système de gestion RH avec intelligence artificielle pour la génération automatique d'attestations
            </p>
          </div>

          <div className="space-y-4">
            <div className="flex items-center space-x-3 text-gray-700">
              <Shield className="h-5 w-5 text-blue-600" />
              <span>Authentification sécurisée</span>
            </div>
            <div className="flex items-center space-x-3 text-gray-700">
              <Bot className="h-5 w-5 text-blue-600" />
              <span>Génération IA d'attestations</span>
            </div>
            <div className="flex items-center space-x-3 text-gray-700">
              <Users className="h-5 w-5 text-blue-600" />
              <span>Gestion des employés</span>
            </div>
          </div>
        </div>

        {/* Panneau de droite - Connexion */}
        <Card className="w-full">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl">Connexion</CardTitle>
            <CardDescription>
              Accédez à votre espace RH
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">



            {/* Formulaire de connexion */}
            <form onSubmit={handleSubmit} className="space-y-4">
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  placeholder="<EMAIL>"
                  className="h-11"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Mot de passe</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    placeholder="••••••••"
                    className="h-11 pr-10"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
              </div>

              <Button
                type="submit"
                className="w-full h-11 bg-blue-600 hover:bg-blue-700"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Connexion en cours...
                  </>
                ) : (
                  "Se connecter"
                )}
              </Button>
            </form>

            <div className="text-center text-xs text-gray-500">
              Connectez-vous avec vos identifiants d'entreprise
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
