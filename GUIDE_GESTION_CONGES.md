# 📅 Guide de Gestion des Congés

## 🎯 Vue d'ensemble

La page de gestion des congés vous permet de gérer toutes les demandes de congés des employés avec des fonctionnalités complètes de validation, rejet et suppression.

## ✨ Fonctionnalités principales

### 📊 **Tableau de bord des statistiques**
- **Total** : Nombre total de demandes de congés
- **En attente** : Demandes nécessitant une action
- **Validées** : Congés approuvés
- **Rejetées** : Demandes refusées

### 🔍 **Filtres avancés**
- **Par statut** : EN_ATTENTE, VALIDEE, REJETEE, EN_COURS, ANNULEE
- **Par département** : Filtrage par service
- **Réinitialisation** : Bouton pour effacer tous les filtres

### 📋 **Table des congés**
Affichage détaillé avec :
- **Employé** : Nom et email
- **Département** : Service de l'employé
- **Dates** : Période de congé (du/au)
- **Durée** : Nombre de jours calculé automatiquement
- **Motif** : Raison de la demande
- **Statut** : Badge coloré selon l'état
- **Priorité** : Niveau d'urgence
- **Date de demande** : Quand la demande a été soumise

## 🎛️ Actions disponibles

### 👁️ **Voir les détails**
- Informations complètes de la demande
- Historique des actions
- Commentaires et motifs

### ✅ **Valider un congé**
- Validation en un clic
- Possibilité d'ajouter des commentaires
- Mise à jour automatique du statut

### ❌ **Rejeter un congé**
- Dialogue de rejet avec motif obligatoire
- Notification automatique à l'employé
- Traçabilité complète

### 🗑️ **Supprimer une demande**
- Suppression définitive avec confirmation
- Sécurité : demande de confirmation
- Action irréversible

## 🎨 Interface utilisateur

### 🏷️ **Badges de statut**
- 🟠 **En attente** : Demande non traitée
- 🟢 **Validée** : Congé approuvé
- 🔴 **Rejetée** : Demande refusée
- 🔵 **En cours** : Traitement en cours
- ⚫ **Annulée** : Demande annulée

### 🎯 **Badges de priorité**
- 🔴 **Urgente** : Traitement immédiat
- 🟠 **Haute** : Priorité élevée
- 🟡 **Normale** : Priorité standard
- ⚪ **Basse** : Peut attendre

## 📱 Responsive Design

- **Desktop** : Table complète avec toutes les colonnes
- **Tablet** : Colonnes adaptées
- **Mobile** : Vue optimisée pour petits écrans

## 🔐 Sécurité et permissions

- **Accès RH/Admin** : Toutes les actions
- **Accès Manager** : Validation/rejet dans son département
- **Traçabilité** : Historique de toutes les actions
- **Confirmations** : Dialogues de sécurité pour actions critiques

## 📊 Données de test

7 demandes de congés créées automatiquement :

1. **Congés d'été 2024** - Admin Système (15 jours) - EN_ATTENTE
2. **Congés de Noël** - Marie Dupont (11 jours) - EN_ATTENTE  
3. **Congé maladie** - Pierre Martin (15 jours) - VALIDEE
4. **Congé parental** - Admin Système (62 jours) - EN_ATTENTE
5. **Pont du 1er mai** - Marie Dupont (2 jours) - REJETEE
6. **Congés de printemps** - Pierre Martin (12 jours) - EN_ATTENTE
7. **Formation externe** - Admin Système (3 jours) - VALIDEE

## 🚀 Comment utiliser

### 1. **Accéder à la page**
- Menu latéral → "Congés"
- URL directe : `/dashboard/conges`

### 2. **Filtrer les demandes**
- Sélectionnez un statut dans le filtre
- Choisissez un département
- Cliquez "Réinitialiser" pour tout afficher

### 3. **Traiter une demande**
- Cliquez sur l'icône 👁️ pour voir les détails
- Cliquez sur ✅ pour valider directement
- Cliquez sur ❌ pour rejeter avec motif
- Cliquez sur 🗑️ pour supprimer

### 4. **Dialogue de détails**
- Vue complète de la demande
- Actions rapides depuis le dialogue
- Informations de traçabilité

### 5. **Dialogue de rejet**
- Motif obligatoire
- Message transmis à l'employé
- Confirmation avant action

## 💡 Bonnes pratiques

### ✅ **À faire**
- Toujours indiquer un motif lors du rejet
- Vérifier les dates et la durée
- Consulter les détails avant validation
- Utiliser les filtres pour organiser le travail

### ❌ **À éviter**
- Supprimer sans raison valable
- Valider sans vérifier les dates
- Rejeter sans motif explicite
- Ignorer les demandes urgentes

## 🔄 Workflow recommandé

1. **Tri par priorité** : Traiter d'abord les urgentes
2. **Vérification** : Consulter les détails
3. **Validation** : Approuver si conforme
4. **Rejet motivé** : Expliquer si refus
5. **Suivi** : Vérifier les statistiques

## 📈 Évolutions futures

- **Calendrier visuel** : Vue calendrier des congés
- **Conflits** : Détection automatique de chevauchements
- **Notifications** : Alertes automatiques
- **Rapports** : Export des données
- **Approbation en lot** : Validation multiple
- **Intégration email** : Notifications automatiques

---

🎉 **La gestion des congés est maintenant complète et opérationnelle !**
