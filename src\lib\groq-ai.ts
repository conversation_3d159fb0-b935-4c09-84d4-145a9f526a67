import Groq from 'groq-sdk';

// Configuration Groq (IA gratuite)
const groq = new Groq({
  apiKey: process.env.GROQ_API_KEY,
});

// Types pour les attestations
export type TypeAttestation = 'travail' | 'stage';

export interface DonneesEmploye {
  nom: string;
  prenom: string;
  poste?: string;
  departement?: string;
  dateEmbauche?: Date;
  dateDebutStage?: Date;
  dateFinStage?: Date;
  etablissement?: string;
  motif?: string;
}

// Modèles d'attestation personnalisables
export const MODELES_ATTESTATION = {
  travail: {
    titre: "ATTESTATION DE TRAVAIL",
    template: `Je soussigné(e), {fonction_signataire}, certifie par la présente que :

Madame/Monsieur {prenom} {nom}
{poste_info}
{departement_info}

{statut_emploi} dans notre entreprise {date_embauche_info} {poste_detail}.

{performance_info}

Cette attestation est délivrée à l'intéressé(e) pour {motif} et pour servir et valoir ce que de droit.

Fait à {lieu}, le {date_actuelle}

{signature_bloc}`,
    variables: {
      fonction_signataire: "Directeur des Ressources Humaines",
      lieu: "Paris",
      signature_bloc: "Le Directeur des Ressources Humaines\n[Signature et cachet]"
    }
  },

  stage: {
    titre: "ATTESTATION DE STAGE",
    template: `Je soussigné(e), {fonction_signataire}, certifie par la présente que :

Madame/Monsieur {prenom} {nom}
{etablissement_info}

a effectué un stage dans notre entreprise {periode_stage} {poste_detail}.
{departement_info}

{evaluation_stage}

{competences_acquises}

Cette attestation est délivrée à l'intéressé(e) pour {motif} et pour servir et valoir ce que de droit.

Fait à {lieu}, le {date_actuelle}

{signature_bloc}`,
    variables: {
      fonction_signataire: "Directeur des Ressources Humaines",
      lieu: "Paris",
      signature_bloc: "Le Directeur des Ressources Humaines\n[Signature et cachet]"
    }
  }
};

// Fonction pour générer une attestation avec Groq (IA gratuite)
export async function genererAttestationAvecGroq(
  type: TypeAttestation,
  donnees: DonneesEmploye,
  modelePersonnalise?: string
): Promise<string> {
  try {
    // Vérifier que la clé API est configurée
    if (!process.env.GROQ_API_KEY) {
      throw new Error('Clé API Groq non configurée');
    }

    const modele = MODELES_ATTESTATION[type];
    const template = modelePersonnalise || modele.template;

    // Créer le prompt pour l'IA
    const prompt = `Tu es un assistant RH professionnel. Génère une attestation ${type === 'travail' ? 'de travail' : 'de stage'} officielle en français en utilisant ce modèle et ces informations :

MODÈLE À SUIVRE :
${template}

INFORMATIONS DE LA PERSONNE :
- Nom : ${donnees.nom}
- Prénom : ${donnees.prenom}
- Poste : ${donnees.poste || 'Non spécifié'}
- Département : ${donnees.departement || 'Non spécifié'}
${type === 'travail' ? `- Date d'embauche : ${donnees.dateEmbauche?.toLocaleDateString('fr-FR') || 'Non spécifiée'}` : ''}
${type === 'stage' ? `- Date début stage : ${donnees.dateDebutStage?.toLocaleDateString('fr-FR') || 'Non spécifiée'}` : ''}
${type === 'stage' ? `- Date fin stage : ${donnees.dateFinStage?.toLocaleDateString('fr-FR') || 'Non spécifiée'}` : ''}
${type === 'stage' ? `- Établissement : ${donnees.etablissement || 'Non spécifié'}` : ''}
- Motif : ${donnees.motif || 'servir et valoir ce que de droit'}

INSTRUCTIONS :
1. Remplace toutes les variables {variable} par les informations appropriées
2. Adapte le contenu selon le type d'attestation
3. Utilise un langage professionnel et officiel
4. Ajoute des détails pertinents selon le contexte
5. Respecte le format français officiel
6. Date actuelle : ${new Date().toLocaleDateString('fr-FR')}
7. Assure-toi que l'attestation fait environ 150-200 mots

Génère uniquement le contenu de l'attestation, sans titre ni formatage markdown.`;

    // Appel à l'API Groq
    const completion = await groq.chat.completions.create({
      messages: [
        {
          role: "system",
          content: "Tu es un assistant RH expert en rédaction d'attestations officielles françaises. Tu génères des documents professionnels, précis et conformes aux standards administratifs."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      model: "llama-3.1-8b-instant", // Modèle gratuit et performant
      max_tokens: 800,
      temperature: 0.3, // Peu de créativité pour un document officiel
    });

    const contenu = completion.choices[0]?.message?.content;

    if (!contenu) {
      throw new Error('Aucun contenu généré par l\'IA');
    }

    return contenu.trim();
  } catch (error) {
    console.error('Erreur lors de la génération avec Groq:', error);

    // Fallback vers la génération manuelle en cas d'erreur
    return genererAttestationManuelle(type, donnees);
  }
}

// Fonction de fallback pour générer une attestation sans IA
function genererAttestationManuelle(type: TypeAttestation, donnees: DonneesEmploye): string {
  const dateActuelle = new Date().toLocaleDateString('fr-FR');
  const modele = MODELES_ATTESTATION[type];

  let contenu = modele.template;

  // Remplacer les variables de base
  contenu = contenu
    .replace('{fonction_signataire}', modele.variables.fonction_signataire)
    .replace('{prenom}', donnees.prenom)
    .replace('{nom}', donnees.nom)
    .replace('{lieu}', modele.variables.lieu)
    .replace('{date_actuelle}', dateActuelle)
    .replace('{signature_bloc}', modele.variables.signature_bloc)
    .replace('{motif}', donnees.motif || 'servir et valoir ce que de droit');

  // Variables spécifiques selon le type
  if (type === 'travail') {
    contenu = contenu
      .replace('{poste_info}', donnees.poste ? `Poste occupé : ${donnees.poste}` : '')
      .replace('{departement_info}', donnees.departement ? `Département : ${donnees.departement}` : '')
      .replace('{statut_emploi}', 'est employé(e)')
      .replace('{date_embauche_info}', donnees.dateEmbauche ? `depuis le ${donnees.dateEmbauche.toLocaleDateString('fr-FR')}` : '')
      .replace('{poste_detail}', donnees.poste ? `en qualité de ${donnees.poste}` : '')
      .replace('{performance_info}', 'Durant cette période, l\'intéressé(e) a fait preuve de sérieux et de compétence dans l\'exercice de ses fonctions.');
  } else {
    contenu = contenu
      .replace('{etablissement_info}', donnees.etablissement ? `Étudiant(e) à ${donnees.etablissement}` : '')
      .replace('{periode_stage}',
        donnees.dateDebutStage && donnees.dateFinStage
          ? `du ${donnees.dateDebutStage.toLocaleDateString('fr-FR')} au ${donnees.dateFinStage.toLocaleDateString('fr-FR')}`
          : 'durant la période convenue'
      )
      .replace('{poste_detail}', donnees.poste ? `en qualité de ${donnees.poste}` : '')
      .replace('{departement_info}', donnees.departement ? `Au sein du département ${donnees.departement}.` : '')
      .replace('{evaluation_stage}', 'Le stage s\'est déroulé dans de bonnes conditions.')
      .replace('{competences_acquises}', 'L\'intéressé(e) a pu acquérir une expérience professionnelle enrichissante.');
  }

  // Nettoyer les lignes vides multiples
  contenu = contenu.replace(/\n\s*\n\s*\n/g, '\n\n');

  return contenu;
}

// Fonction pour tester la connexion Groq
export async function testerConnexionGroq(): Promise<boolean> {
  try {
    if (!process.env.GROQ_API_KEY) {
      return false;
    }

    const completion = await groq.chat.completions.create({
      messages: [{ role: "user", content: "Test de connexion" }],
      model: "llama-3.1-8b-instant",
      max_tokens: 5,
    });

    return !!completion.choices[0]?.message?.content;
  } catch (error) {
    console.error('Erreur de connexion Groq:', error);
    return false;
  }
}

// Fonction pour obtenir les modèles disponibles
export function obtenirModelesDisponibles() {
  return MODELES_ATTESTATION;
}
