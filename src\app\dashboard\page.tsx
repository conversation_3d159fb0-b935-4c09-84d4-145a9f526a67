import { AppSidebar } from "@/components/app-sidebar"
import { SectionCards } from "@/components/section-cards"
import { DemandesStats } from "@/components/demandes-stats"
import { RecentDemandes } from "@/components/recent-demandes"
import { SiteHeader } from "@/components/site-header"
import {
  SidebarInset,
  SidebarProvider,
} from "@/components/ui/sidebar"
import { getDemandes, getUsers, getStatistics } from "@/lib/data"
import { requireAuth } from "@/lib/auth-utils"

export default async function Page() {
  // Vérifier l'authentification
  const currentUser = await requireAuth()

  // Récupération des données depuis la base de données
  const [demandes, users, statistics] = await Promise.all([
    getDemandes(),
    getUsers(),
    getStatistics(),
  ])

  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
          "--header-height": "calc(var(--spacing) * 12)",
        } as React.CSSProperties
      }
    >
      <AppSidebar variant="inset" />
      <SidebarInset>
        <SiteHeader />
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
              <SectionCards statistics={statistics} />
              <div className="px-4 lg:px-6">
                <DemandesStats demandes={demandes} />
              </div>
              <div className="px-4 lg:px-6">
                <RecentDemandes demandes={demandes} />
              </div>
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
