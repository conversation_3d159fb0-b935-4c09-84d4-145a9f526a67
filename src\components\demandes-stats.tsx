"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { IconBuilding, IconClock, IconCheck, IconX, IconFileText } from "@tabler/icons-react"

interface DemandesStatsProps {
  demandes: any[] // Type temporaire, on affinera plus tard
}

export function DemandesStats({ demandes }: DemandesStatsProps) {
  // Statistiques par département
  const departements = [...new Set(demandes.map(d => d.user?.departement || 'Non défini').filter(Boolean))]
  const statsDepartements = departements.map(dept => {
    const demandesDept = demandes.filter(d => d.user?.departement === dept)
    const enAttente = demandesDept.filter(d => d.status === "pending").length
    const validees = demandesDept.filter(d => d.status === "approved").length
    const rejetees = demandesDept.filter(d => d.status === "rejected").length
    const enCours = demandesDept.filter(d => d.status === "in_progress").length

    return {
      departement: dept,
      total: demandesDept.length,
      enAttente,
      validees,
      rejetees,
      enCours,
      tauxTraitement: demandesDept.length > 0 ? Math.round(((validees + rejetees) / demandesDept.length) * 100) : 0
    }
  })

  // Statistiques par type de demande
  const types = [...new Set(demandes.map(d => d.type))]
  const statsTypes = types.map(type => {
    const demandesType = demandes.filter(d => d.type === type)
    const enAttente = demandesType.filter(d => d.status === "pending").length
    const validees = demandesType.filter(d => d.status === "approved").length

    return {
      type,
      total: demandesType.length,
      enAttente,
      validees,
      pourcentage: demandes.length > 0 ? Math.round((demandesType.length / demandes.length) * 100) : 0
    }
  })

  const getTypeLabel = (type: string) => {
    switch (type) {
      case "conge": return "Congés"
      case "materiel": return "Matériel"
      case "attestation": return "Attestations"
      default: return type
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "conge": return <IconClock className="h-4 w-4" />
      case "materiel": return <IconFileText className="h-4 w-4" />
      case "attestation": return <IconFileText className="h-4 w-4" />
      default: return <IconFileText className="h-4 w-4" />
    }
  }

  return (
    <div className="grid gap-4 md:grid-cols-2">
      {/* Statistiques par département */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <IconBuilding className="h-5 w-5" />
            Demandes par département
          </CardTitle>
          <CardDescription>
            Répartition et statut des demandes par département
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {statsDepartements.map((dept) => (
              <div key={dept.departement} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{dept.departement}</span>
                    <Badge variant="outline">{dept.total} demandes</Badge>
                  </div>
                  <span className="text-sm text-muted-foreground">
                    {dept.tauxTraitement}% traité
                  </span>
                </div>
                <Progress value={dept.tauxTraitement} className="h-2" />
                <div className="flex gap-4 text-xs text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <IconClock className="h-3 w-3 text-orange-500" />
                    {dept.enAttente} en attente
                  </span>
                  <span className="flex items-center gap-1">
                    <IconCheck className="h-3 w-3 text-green-500" />
                    {dept.validees} validées
                  </span>
                  {dept.rejetees > 0 && (
                    <span className="flex items-center gap-1">
                      <IconX className="h-3 w-3 text-red-500" />
                      {dept.rejetees} rejetées
                    </span>
                  )}
                  {dept.enCours > 0 && (
                    <span className="flex items-center gap-1">
                      <IconClock className="h-3 w-3 text-blue-500" />
                      {dept.enCours} en cours
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Statistiques par type */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <IconFileText className="h-5 w-5" />
            Demandes par type
          </CardTitle>
          <CardDescription>
            Répartition des demandes par type et leur statut
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {statsTypes.map((typeStats) => (
              <div key={typeStats.type} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getTypeIcon(typeStats.type)}
                    <span className="font-medium">{getTypeLabel(typeStats.type)}</span>
                    <Badge variant="secondary">{typeStats.total}</Badge>
                  </div>
                  <span className="text-sm text-muted-foreground">
                    {typeStats.pourcentage}% du total
                  </span>
                </div>
                <Progress value={typeStats.pourcentage} className="h-2" />
                <div className="flex gap-4 text-xs text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <IconClock className="h-3 w-3 text-orange-500" />
                    {typeStats.enAttente} en attente
                  </span>
                  <span className="flex items-center gap-1">
                    <IconCheck className="h-3 w-3 text-green-500" />
                    {typeStats.validees} validées
                  </span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
