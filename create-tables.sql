-- Script SQL pour créer toutes les tables du système RH
-- À exécuter dans l'éditeur SQL de Supabase

-- Création des types ENUM
CREATE TYPE "Role" AS ENUM ('EMPLOYE', 'MANAGER', 'RH', 'ADMIN');
CREATE TYPE "StatutEmploye" AS ENUM ('ACTIF', 'INACTIF', 'CONGE', 'SUSPENDU');
CREATE TYPE "TypeDemande" AS ENUM ('CONGE', 'MATERIEL', 'ATTESTATION', 'FORMATION', 'AUTRE');
CREATE TYPE "StatutDemande" AS ENUM ('EN_ATTENTE', 'EN_COURS', 'VALIDEE', 'REJETEE', 'ANNULEE');
CREATE TYPE "Priorite" AS ENUM ('BASSE', 'NORMALE', 'HAUTE', 'URGENTE');
CREATE TYPE "StatutAttestation" AS ENUM ('GENEREE', 'VALIDEE', 'ENVOYEE', 'ARCHIVEE');
CREATE TYPE "TypeNotification" AS ENUM ('INFO', 'SUCCES', 'AVERTISSEMENT', 'ERREUR', 'DEMANDE', 'VALIDATION', 'REJET');
CREATE TYPE "TypeParametre" AS ENUM ('TEXTE', 'NOMBRE', 'BOOLEEN', 'JSON', 'EMAIL', 'URL');

-- Table des utilisateurs
CREATE TABLE "users" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "nom" TEXT NOT NULL,
    "prenom" TEXT,
    "poste" TEXT,
    "departement" TEXT,
    "manager" TEXT,
    "telephone" TEXT,
    "adresse" TEXT,
    "dateEmbauche" TIMESTAMP(3),
    "salaire" DOUBLE PRECISION,
    "congesRestants" INTEGER NOT NULL DEFAULT 25,
    "role" "Role" NOT NULL DEFAULT 'EMPLOYE',
    "statut" "StatutEmploye" NOT NULL DEFAULT 'ACTIF',
    "avatar" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- Table des demandes
CREATE TABLE "demandes" (
    "id" TEXT NOT NULL,
    "type" "TypeDemande" NOT NULL,
    "statut" "StatutDemande" NOT NULL DEFAULT 'EN_ATTENTE',
    "priorite" "Priorite" NOT NULL DEFAULT 'NORMALE',
    "titre" TEXT,
    "description" TEXT NOT NULL,
    "motif" TEXT,
    "dateDebut" TIMESTAMP(3),
    "dateFin" TIMESTAMP(3),
    "materiel" TEXT,
    "quantite" INTEGER,
    "budgetEstime" DOUBLE PRECISION,
    "justification" TEXT,
    "typeAttestation" TEXT,
    "motifAttestation" TEXT,
    "langue" TEXT NOT NULL DEFAULT 'français',
    "dateTraitement" TIMESTAMP(3),
    "motifRejet" TEXT,
    "commentaires" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT NOT NULL,

    CONSTRAINT "demandes_pkey" PRIMARY KEY ("id")
);

-- Table des attestations
CREATE TABLE "attestations" (
    "id" TEXT NOT NULL,
    "contenu" TEXT NOT NULL,
    "statut" "StatutAttestation" NOT NULL DEFAULT 'GENEREE',
    "genereParIA" BOOLEAN NOT NULL DEFAULT true,
    "modeleUtilise" TEXT,
    "dateEnvoi" TIMESTAMP(3),
    "emailEnvoye" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "demandeId" TEXT NOT NULL,

    CONSTRAINT "attestations_pkey" PRIMARY KEY ("id")
);

-- Table des notifications
CREATE TABLE "notifications" (
    "id" TEXT NOT NULL,
    "titre" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "type" "TypeNotification" NOT NULL DEFAULT 'INFO',
    "lu" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "userId" TEXT NOT NULL,

    CONSTRAINT "notifications_pkey" PRIMARY KEY ("id")
);

-- Table des paramètres
CREATE TABLE "parametres" (
    "id" TEXT NOT NULL,
    "cle" TEXT NOT NULL,
    "valeur" TEXT NOT NULL,
    "description" TEXT,
    "type" "TypeParametre" NOT NULL DEFAULT 'TEXTE',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "parametres_pkey" PRIMARY KEY ("id")
);

-- Table des templates d'attestations
CREATE TABLE "templates_attestations" (
    "id" TEXT NOT NULL,
    "nom" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "contenu" TEXT NOT NULL,
    "actif" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "templates_attestations_pkey" PRIMARY KEY ("id")
);

-- Table de l'historique des actions
CREATE TABLE "historique_actions" (
    "id" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "details" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "userId" TEXT,
    "demandeId" TEXT,

    CONSTRAINT "historique_actions_pkey" PRIMARY KEY ("id")
);

-- Création des index uniques
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");
CREATE UNIQUE INDEX "attestations_demandeId_key" ON "attestations"("demandeId");
CREATE UNIQUE INDEX "parametres_cle_key" ON "parametres"("cle");

-- Création des clés étrangères
ALTER TABLE "demandes" ADD CONSTRAINT "demandes_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "attestations" ADD CONSTRAINT "attestations_demandeId_fkey" FOREIGN KEY ("demandeId") REFERENCES "demandes"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "notifications" ADD CONSTRAINT "notifications_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
