"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { AppSidebar } from "@/components/app-sidebar"
import { SiteHeaderClient } from "@/components/site-header-client"
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, Users, Shield, CheckCircle, AlertCircle } from "lucide-react"

interface User {
  id: string
  email: string
  name: string | null
  prenom: string | null
  nom: string | null
  role: string
  statut: string
  createdAt: string
}

const ROLES = [
  { value: "EMPLOYE", label: "Employé", color: "bg-blue-100 text-blue-800" },
  { value: "MANAGER", label: "Manager", color: "bg-green-100 text-green-800" },
  { value: "RH", label: "RH", color: "bg-purple-100 text-purple-800" },
  { value: "ADMIN", label: "Administrateur", color: "bg-red-100 text-red-800" },
]

export default function GestionRolesPage() {
  const { data: session } = useSession()
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState<string | null>(null)
  const [message, setMessage] = useState<{ type: "success" | "error", text: string } | null>(null)

  // Vérifier que l'utilisateur est admin
  const isAdmin = session?.user?.role === "ADMIN"

  useEffect(() => {
    if (isAdmin) {
      fetchUsers()
    }
  }, [isAdmin])

  const fetchUsers = async () => {
    try {
      const response = await fetch("/api/users")
      if (response.ok) {
        const data = await response.json()
        setUsers(data)
      } else {
        setMessage({ type: "error", text: "Erreur lors du chargement des utilisateurs" })
      }
    } catch (error) {
      setMessage({ type: "error", text: "Erreur de connexion" })
    } finally {
      setLoading(false)
    }
  }

  const updateUserRole = async (userId: string, newRole: string) => {
    console.log(`🔄 Tentative de mise à jour du rôle: ${userId} → ${newRole}`)

    setUpdating(userId)
    setMessage(null)

    try {
      const response = await fetch(`/api/users/${userId}/role`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ role: newRole }),
      })

      console.log(`📡 Réponse API: ${response.status}`)

      if (response.ok) {
        const result = await response.json()
        console.log(`✅ Succès:`, result)

        // Mettre à jour la liste locale
        setUsers(users.map(user =>
          user.id === userId ? { ...user, role: newRole } : user
        ))
        setMessage({ type: "success", text: `Rôle mis à jour: ${newRole}` })
      } else {
        const error = await response.json()
        console.error(`❌ Erreur API:`, error)
        setMessage({ type: "error", text: error.message || "Erreur lors de la mise à jour" })
      }
    } catch (error) {
      console.error(`❌ Erreur réseau:`, error)
      setMessage({ type: "error", text: "Erreur de connexion" })
    } finally {
      setUpdating(null)
    }
  }

  const getRoleInfo = (role: string) => {
    return ROLES.find(r => r.value === role) || ROLES[0]
  }

  if (!isAdmin) {
    return (
      <SidebarProvider>
        <AppSidebar variant="inset" />
        <SidebarInset>
          <SiteHeaderClient />
          <div className="flex flex-1 flex-col gap-4 p-4">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Accès refusé. Seuls les administrateurs peuvent gérer les rôles.
              </AlertDescription>
            </Alert>
          </div>
        </SidebarInset>
      </SidebarProvider>
    )
  }

  return (
    <SidebarProvider>
      <AppSidebar variant="inset" />
      <SidebarInset>
        <SiteHeaderClient />
        <div className="flex flex-1 flex-col gap-4 p-4">
          <div className="flex items-center gap-2">
            <Shield className="h-6 w-6" />
            <h1 className="text-2xl font-bold">Gestion des Rôles</h1>
          </div>

          {message && (
            <Alert variant={message.type === "error" ? "destructive" : "default"}>
              {message.type === "success" ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <AlertCircle className="h-4 w-4" />
              )}
              <AlertDescription>{message.text}</AlertDescription>
            </Alert>
          )}

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Utilisateurs et Rôles
              </CardTitle>
              <CardDescription>
                Gérez les rôles et permissions des utilisateurs du système
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin" />
                  <span className="ml-2">Chargement des utilisateurs...</span>
                </div>
              ) : (
                <div className="space-y-4">
                  {users.length === 0 ? (
                    <p className="text-center text-gray-500 py-8">
                      Aucun utilisateur trouvé
                    </p>
                  ) : (
                    users.map((user) => {
                      const roleInfo = getRoleInfo(user.role)
                      const isCurrentUser = user.id === session?.user?.id

                      return (
                        <div
                          key={user.id}
                          className="flex items-center justify-between p-4 border rounded-lg"
                        >
                          <div className="flex-1">
                            <div className="flex items-center gap-3">
                              <div>
                                <h3 className="font-medium">
                                  {user.name || `${user.prenom} ${user.nom}`}
                                </h3>
                                <p className="text-sm text-gray-500">{user.email}</p>
                                {isCurrentUser && (
                                  <Badge variant="outline" className="text-xs mt-1">
                                    Vous
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center gap-4">
                            <Badge className={roleInfo.color}>
                              {roleInfo.label}
                            </Badge>

                            {!isCurrentUser && (
                              <div className="flex gap-2">
                                {ROLES.map((role) => (
                                  <Button
                                    key={role.value}
                                    size="sm"
                                    variant={user.role === role.value ? "default" : "outline"}
                                    onClick={() => updateUserRole(user.id, role.value)}
                                    disabled={updating === user.id}
                                    className="text-xs"
                                  >
                                    {role.label}
                                  </Button>
                                ))}
                              </div>
                            )}

                            {isCurrentUser && (
                              <p className="text-sm text-gray-500">
                                Votre rôle ne peut pas être modifié
                              </p>
                            )}

                            {updating === user.id && (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            )}
                          </div>
                        </div>
                      )
                    })
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Légende des rôles */}
          <Card>
            <CardHeader>
              <CardTitle>Description des Rôles</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Badge className="bg-red-100 text-red-800">Administrateur</Badge>
                    <span className="text-sm">Accès complet au système</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className="bg-purple-100 text-purple-800">RH</Badge>
                    <span className="text-sm">Gestion des employés et demandes</span>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Badge className="bg-green-100 text-green-800">Manager</Badge>
                    <span className="text-sm">Gestion d'équipe et validation</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className="bg-blue-100 text-blue-800">Employé</Badge>
                    <span className="text-sm">Accès de base aux fonctionnalités</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
