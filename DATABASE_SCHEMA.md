# Schéma de Base de Données - Système de Gestion RH

## Vue d'ensemble

Ce schéma Prisma est conçu pour un système complet de gestion des demandes RH avec les fonctionnalités suivantes :

- Gestion des employés et utilisateurs
- Système de demandes (congés, matériel, attestations)
- Génération automatique d'attestations avec IA
- Système de notifications
- Paramètres configurables
- Historique des actions

## Modèles Principaux

### 1. User (Utilisateurs/Employés)
```prisma
model User {
  // Informations personnelles
  nom, prenom, email
  
  // Informations professionnelles
  poste, departement, manager
  dateEmbauche, salaire, congesRestants
  
  // Système
  role (EMPLOYE, MANAGER, RH, ADMIN)
  statut (ACTIF, INACTIF, CONGE, SUSPENDU)
}
```

### 2. Demande (Demandes des employés)
```prisma
model Demande {
  // Type et statut
  type (CONGE, MATERIEL, ATTESTATION, FORMATION, AUTRE)
  statut (EN_ATTENTE, EN_COURS, VALIDEE, REJETEE, ANNULEE)
  priorite (BASSE, NORMALE, HAUTE, URGENTE)
  
  // Détails spécifiques par type
  // Congés: dateDebut, dateFin, motif
  // Matériel: materiel, quantite, budgetEstime
  // Attestations: typeAttestation, motifAttestation
}
```

### 3. Attestation (Attestations générées)
```prisma
model Attestation {
  contenu // Contenu généré par IA
  statut (GENEREE, VALIDEE, ENVOYEE, ARCHIVEE)
  genereParIA // Booléen pour traçabilité
  dateEnvoi, emailEnvoye
}
```

### 4. Notification (Système de notifications)
```prisma
model Notification {
  titre, message
  type (INFO, SUCCES, AVERTISSEMENT, ERREUR, DEMANDE, VALIDATION, REJET)
  lu // Statut de lecture
}
```

## Fonctionnalités Supportées

### Dashboard (/dashboard)
- **Statistiques calculées** : nombre de demandes par statut, taux de traitement
- **Graphiques** : évolution par département, répartition par type
- **Accès rapide** : liens vers les sections principales

### Gestion des Demandes (/dashboard/demandes)
- **Liste filtrable** : par type, statut, date, employé, département
- **Actions** : valider, rejeter, modifier, supprimer
- **Détails** : page individuelle pour chaque demande
- **Génération d'attestations** : automatique avec IA

### Gestion des Attestations (/dashboard/attestations)
- **Liste des attestations** : avec statut (générée, envoyée, en attente)
- **Aperçu PDF** : visualisation du contenu
- **Actions** : valider, modifier, envoyer à l'employé
- **Templates** : modèles configurables avec variables

### Gestion des Utilisateurs (/dashboard/utilisateurs)
- **Liste des employés** : nom, poste, email, date d'entrée
- **Historique** : accès rapide aux demandes de chaque employé
- **Rôles** : modification des permissions (optionnel)

### Notifications (/dashboard/notifications)
- **Envoi ciblé** : à un ou plusieurs utilisateurs
- **Historique** : notifications envoyées
- **Types** : validation, rejet, information, etc.

### Paramètres (/dashboard/settings)
- **Types de demandes** : configuration des types disponibles
- **Templates d'attestations** : modèles avec variables IA
- **Informations organisation** : nom, logo, adresse, etc.

## Installation et Configuration

### 1. Installation des dépendances
```bash
npm install prisma @prisma/client
npm install -D prisma
```

### 2. Configuration de la base de données
```bash
# Créer le fichier .env avec DATABASE_URL
echo "DATABASE_URL='postgresql://user:password@localhost:5432/rhmanager'" > .env

# Initialiser Prisma
npx prisma init

# Appliquer le schéma
npx prisma db push

# Générer le client
npx prisma generate
```

### 3. Initialisation avec des données de test
```bash
# Exécuter le seed
npx prisma db seed
```

## Variables d'Environnement Requises

```env
DATABASE_URL="postgresql://user:password@localhost:5432/rhmanager"
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"
OPENAI_API_KEY="your-openai-key" # Pour la génération d'attestations
SMTP_HOST="smtp.gmail.com" # Pour l'envoi d'emails
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
```

## Exemples d'Utilisation

### Créer une demande de congé
```typescript
const demande = await prisma.demande.create({
  data: {
    type: 'CONGE',
    titre: 'Congés d\'été',
    description: 'Demande de congés pour vacances familiales',
    dateDebut: new Date('2024-07-01'),
    dateFin: new Date('2024-07-15'),
    userId: 'user-id',
  },
})
```

### Générer une attestation
```typescript
const attestation = await prisma.attestation.create({
  data: {
    contenu: contenuGenereParIA,
    statut: 'GENEREE',
    genereParIA: true,
    demandeId: 'demande-id',
  },
})
```

### Récupérer les statistiques du dashboard
```typescript
const stats = {
  totalDemandes: await prisma.demande.count(),
  demandesEnAttente: await prisma.demande.count({
    where: { statut: 'EN_ATTENTE' }
  }),
  // ... autres statistiques
}
```

## Sécurité et Permissions

### Rôles et Accès
- **EMPLOYE** : Créer des demandes, voir ses propres demandes
- **MANAGER** : Valider les demandes de son équipe
- **RH** : Accès complet aux demandes et attestations
- **ADMIN** : Accès système complet

### Validation des Données
- Contraintes de base de données (unique, required)
- Validation côté application avec Zod
- Middleware d'authentification et d'autorisation

## Évolutions Futures

### Fonctionnalités Additionnelles
- **Workflow personnalisé** : règles de validation par département
- **Intégration calendrier** : synchronisation avec Google Calendar
- **Rapports avancés** : export PDF, Excel
- **API mobile** : application mobile pour les employés
- **Signature électronique** : pour les attestations importantes

### Optimisations
- **Cache Redis** : pour les statistiques fréquemment consultées
- **Indexation** : sur les champs de recherche fréquents
- **Archivage** : des anciennes demandes et attestations
