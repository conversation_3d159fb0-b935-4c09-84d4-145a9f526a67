"use server"

import { prisma } from './prisma'
import { revalidatePath } from 'next/cache'

// CREATE - Créer une nouvelle demande
export async function createDemande(data: {
  type: string
  message: string
  userId: string
}) {
  try {
    const demande = await prisma.demande.create({
      data: {
        type: data.type,
        message: data.message,
        status: 'pending',
        userId: data.userId,
      },
      include: {
        user: true,
      },
    })

    // Créer une notification automatique pour les RH
    await createNotification({
      titre: `Nouvelle demande: ${data.type}`,
      message: `${demande.user.name} a créé une nouvelle demande de ${data.type.toLowerCase()}. Message: ${data.message}`,
      type: 'INFO',
      demandeId: demande.id,
    })

    revalidatePath('/dashboard')
    revalidatePath('/dashboard/demandes')

    return { success: true, data: demande }
  } catch (error) {
    console.error('Erreur lors de la création de la demande:', error)
    return { success: false, error: 'Erreur lors de la création de la demande' }
  }
}

// UPDATE - Mettre à jour le statut d'une demande
export async function updateDemandeStatus(
  demandeId: string,
  statut: string,
  commentaires?: string
) {
  try {
    // Mapper les nouveaux statuts vers les anciens pour compatibilité
    const statusMapping: { [key: string]: string } = {
      'VALIDEE': 'approved',
      'REJETEE': 'rejected',
      'EN_ATTENTE': 'pending',
      'EN_COURS': 'in_progress',
      'ANNULEE': 'rejected',
      // Garder les anciens statuts pour compatibilité
      'approved': 'approved',
      'rejected': 'rejected',
      'pending': 'pending',
      'in_progress': 'in_progress'
    }

    const oldStatus = statusMapping[statut] || statut

    const updateData: any = {
      statut: statut, // Nouveau champ statut
      status: oldStatus, // Ancien champ status pour compatibilité
      dateTraitement: statut !== 'EN_ATTENTE' && statut !== 'pending' ? new Date() : null,
    }

    // Ajouter les commentaires ou motif de rejet
    if (commentaires) {
      if (statut === 'REJETEE' || statut === 'rejected') {
        updateData.motifRejet = commentaires
      } else {
        updateData.commentaires = commentaires
      }
    }

    const demande = await prisma.demande.update({
      where: { id: demandeId },
      data: updateData,
      include: {
        user: true,
      },
    })

    revalidatePath('/dashboard')
    revalidatePath('/dashboard/demandes')
    revalidatePath('/dashboard/conges')

    return { success: true, data: demande }
  } catch (error) {
    console.error('Erreur lors de la mise à jour de la demande:', error)
    return { success: false, error: 'Erreur lors de la mise à jour de la demande' }
  }
}

// DELETE - Supprimer une demande
export async function deleteDemande(demandeId: string) {
  try {
    await prisma.demande.delete({
      where: { id: demandeId },
    })

    revalidatePath('/dashboard')
    revalidatePath('/dashboard/demandes')
    revalidatePath('/dashboard/conges')

    return { success: true }
  } catch (error) {
    console.error('Erreur lors de la suppression de la demande:', error)
    return { success: false, error: 'Erreur lors de la suppression de la demande' }
  }
}

// NOTIFICATIONS - Marquer une notification comme lue
export async function markNotificationAsRead(notificationId: string) {
  try {
    await prisma.notification.update({
      where: { id: notificationId },
      data: { lu: true, dateLecture: new Date() },
    })

    revalidatePath('/dashboard')
    revalidatePath('/dashboard/notifications')

    return { success: true }
  } catch (error) {
    console.error('Erreur lors de la mise à jour de la notification:', error)
    return { success: false, error: 'Erreur lors de la mise à jour de la notification' }
  }
}

// NOTIFICATIONS - Marquer toutes les notifications comme lues
export async function markAllNotificationsAsRead() {
  try {
    await prisma.notification.updateMany({
      where: { lu: false },
      data: { lu: true, dateLecture: new Date() },
    })

    revalidatePath('/dashboard')
    revalidatePath('/dashboard/notifications')

    return { success: true }
  } catch (error) {
    console.error('Erreur lors de la mise à jour des notifications:', error)
    return { success: false, error: 'Erreur lors de la mise à jour des notifications' }
  }
}

// NOTIFICATIONS - Créer une notification automatique
export async function createNotification(data: {
  titre: string
  message: string
  type: 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR'
  userId?: string
  demandeId?: string
}) {
  try {
    const notification = await prisma.notification.create({
      data: {
        titre: data.titre,
        message: data.message,
        type: data.type,
        userId: data.userId,
        demandeId: data.demandeId,
        lu: false,
      },
    })

    revalidatePath('/dashboard')
    revalidatePath('/dashboard/notifications')

    return { success: true, data: notification }
  } catch (error) {
    console.error('Erreur lors de la création de la notification:', error)
    return { success: false, error: 'Erreur lors de la création de la notification' }
  }
}

// CREATE USER - Créer un nouvel utilisateur
export async function createUser(data: {
  email: string
  name: string
  nom?: string
  prenom?: string
  poste?: string
  departement?: string
  role?: string
}) {
  try {
    const user = await prisma.user.create({
      data: {
        email: data.email,
        name: data.name,
        nom: data.nom,
        prenom: data.prenom,
        poste: data.poste,
        departement: data.departement,
        role: data.role || 'user',
      },
    })

    revalidatePath('/dashboard')
    revalidatePath('/dashboard/employes')

    return { success: true, data: user }
  } catch (error) {
    console.error('Erreur lors de la création de l\'utilisateur:', error)
    return { success: false, error: 'Erreur lors de la création de l\'utilisateur' }
  }
}

// UPDATE USER - Mettre à jour un utilisateur
export async function updateUser(userId: string, data: {
  name?: string
  nom?: string
  prenom?: string
  poste?: string
  departement?: string
  role?: string
}) {
  try {
    const user = await prisma.user.update({
      where: { id: userId },
      data,
    })

    revalidatePath('/dashboard')
    revalidatePath('/dashboard/employes')

    return { success: true, data: user }
  } catch (error) {
    console.error('Erreur lors de la mise à jour de l\'utilisateur:', error)
    return { success: false, error: 'Erreur lors de la mise à jour de l\'utilisateur' }
  }
}

// CREATE ATTESTATION - Créer une attestation
export async function createAttestation(data: {
  contenu: string
  demandeId: string
  genereParIA?: boolean
}) {
  try {
    const attestation = await prisma.attestation.create({
      data: {
        contenu: data.contenu,
        demandeId: data.demandeId,
        genereParIA: data.genereParIA || false,
        statut: 'GENEREE',
      },
      include: {
        demande: {
          include: {
            user: true,
          },
        },
      },
    })

    // Mettre à jour le statut de la demande
    await prisma.demande.update({
      where: { id: data.demandeId },
      data: { status: 'in_progress' },
    })

    revalidatePath('/dashboard')
    revalidatePath('/dashboard/demandes')
    revalidatePath('/dashboard/attestations')

    return { success: true, data: attestation }
  } catch (error) {
    console.error('Erreur lors de la création de l\'attestation:', error)
    return { success: false, error: 'Erreur lors de la création de l\'attestation' }
  }
}

// GET STATISTICS - Récupérer les statistiques en temps réel
export async function getStatisticsAction() {
  try {
    const [
      totalDemandes,
      demandesEnAttente,
      demandesValidees,
      demandesRejetees,
      demandesEnCours,
      totalUsers,
    ] = await Promise.all([
      prisma.demande.count(),
      prisma.demande.count({ where: { status: 'pending' } }),
      prisma.demande.count({ where: { status: 'approved' } }),
      prisma.demande.count({ where: { status: 'rejected' } }),
      prisma.demande.count({ where: { status: 'in_progress' } }),
      prisma.user.count(),
    ])

    const tauxTraitement = totalDemandes > 0
      ? Math.round(((demandesValidees + demandesRejetees) / totalDemandes) * 100)
      : 0

    return {
      success: true,
      data: {
        totalDemandes,
        demandesEnAttente,
        demandesValidees,
        demandesRejetees,
        demandesEnCours,
        totalUsers,
        tauxTraitement,
      }
    }
  } catch (error) {
    console.error('Erreur lors du calcul des statistiques:', error)
    return { success: false, error: 'Erreur lors du calcul des statistiques' }
  }
}
