// Types basés sur le schéma Prisma pour l'interface utilisateur

export type Role = 'EMPLOYE' | 'MANAGER' | 'RH' | 'ADMIN'

export type StatutEmploye = 'ACTIF' | 'INACTIF' | 'CONGE' | 'SUSPENDU'

export type TypeDemande = 'CONGE' | 'MATERIEL' | 'ATTESTATION' | 'FORMATION' | 'AUTRE'

export type StatutDemande = 'EN_ATTENTE' | 'EN_COURS' | 'VALIDEE' | 'REJETEE' | 'ANNULEE'

export type Priorite = 'BASSE' | 'NORMALE' | 'HAUTE' | 'URGENTE'

export type StatutAttestation = 'GENEREE' | 'VALIDEE' | 'ENVOYEE' | 'ARCHIVEE'

export type TypeNotification = 'INFO' | 'SUCCES' | 'AVERTISSEMENT' | 'ERREUR' | 'DEMANDE' | 'VALIDATION' | 'REJET'

export interface User {
  id: string
  email: string
  nom: string
  prenom?: string
  poste?: string
  departement?: string
  manager?: string
  telephone?: string
  adresse?: string
  dateEmbauche?: Date
  salaire?: number
  congesRestants: number
  role: Role
  statut: StatutEmploye
  avatar?: string
  createdAt: Date
  updatedAt: Date
}

export interface Demande {
  id: string
  type: TypeDemande
  statut: StatutDemande
  priorite: Priorite
  titre?: string
  description: string
  motif?: string
  
  // Dates spécifiques aux congés
  dateDebut?: Date
  dateFin?: Date
  
  // Détails spécifiques au matériel
  materiel?: string
  quantite?: number
  budgetEstime?: number
  justification?: string
  
  // Détails spécifiques aux attestations
  typeAttestation?: string
  motifAttestation?: string
  langue: string
  
  // Traitement
  dateTraitement?: Date
  motifRejet?: string
  commentaires?: string
  
  // Métadonnées
  createdAt: Date
  updatedAt: Date
  
  // Relations
  userId: string
  user?: User
  attestation?: Attestation
}

export interface Attestation {
  id: string
  contenu: string
  statut: StatutAttestation
  genereParIA: boolean
  modeleUtilise?: string
  dateEnvoi?: Date
  emailEnvoye: boolean
  createdAt: Date
  updatedAt: Date
  demandeId: string
  demande?: Demande
}

export interface Notification {
  id: string
  titre: string
  message: string
  type: TypeNotification
  lu: boolean
  createdAt: Date
  userId: string
  user?: User
}

export interface Parametre {
  id: string
  cle: string
  valeur: string
  description?: string
  type: 'TEXTE' | 'NOMBRE' | 'BOOLEEN' | 'JSON' | 'EMAIL' | 'URL'
  createdAt: Date
  updatedAt: Date
}

export interface TemplateAttestation {
  id: string
  nom: string
  type: string
  contenu: string
  actif: boolean
  createdAt: Date
  updatedAt: Date
}

export interface HistoriqueAction {
  id: string
  action: string
  details?: string
  createdAt: Date
  userId?: string
  demandeId?: string
}

// Types pour les statistiques du dashboard
export interface StatistiquesDashboard {
  totalDemandes: number
  demandesEnAttente: number
  demandesValidees: number
  demandesRejetees: number
  demandesEnCours: number
  tauxTraitement: number
  
  // Par département
  statsDepartements: {
    departement: string
    total: number
    enAttente: number
    validees: number
    rejetees: number
    enCours: number
    tauxTraitement: number
  }[]
  
  // Par type
  statsTypes: {
    type: TypeDemande
    total: number
    pourcentage: number
  }[]
}

// Types pour les filtres
export interface FiltresDemandes {
  type?: TypeDemande
  statut?: StatutDemande
  departement?: string
  dateDebut?: Date
  dateFin?: Date
  priorite?: Priorite
  userId?: string
}

// Types pour les actions sur les demandes
export interface ActionDemande {
  id: string
  action: 'VALIDER' | 'REJETER' | 'MODIFIER' | 'SUPPRIMER'
  motif?: string
  commentaires?: string
}

// Types pour la génération d'attestations
export interface GenerationAttestation {
  demandeId: string
  templateId?: string
  contenuPersonnalise?: string
  variables?: Record<string, string>
}
