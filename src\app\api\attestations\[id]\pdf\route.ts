import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { requireAuth } from '@/lib/auth-utils'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Vérifier l'authentification
    const currentUser = await requireAuth()
    if (!currentUser) {
      return NextResponse.json({ error: 'Non autorisé' }, { status: 401 })
    }

    const { id } = params

    // Récupérer l'attestation
    const attestation = await prisma.attestation.findUnique({
      where: { id },
      include: {
        demande: {
          include: {
            user: true,
          },
        },
      },
    })

    if (!attestation) {
      return NextResponse.json({ error: 'Attestation non trouvée' }, { status: 404 })
    }

    // Générer le contenu HTML pour le PDF
    const htmlContent = `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attestation</title>
    <style>
        body {
            font-family: 'Times New Roman', serif;
            line-height: 1.6;
            margin: 40px;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .title {
            font-size: 20px;
            font-weight: bold;
            text-align: center;
            margin: 40px 0;
            text-decoration: underline;
        }
        .content {
            white-space: pre-wrap;
            text-align: justify;
            margin: 30px 0;
        }
        .footer {
            margin-top: 60px;
            text-align: right;
        }
        .signature {
            margin-top: 40px;
            text-align: right;
        }
        .date {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">RH Manager</div>
        <div>Système de Gestion des Ressources Humaines</div>
    </div>
    
    <div class="title">ATTESTATION DE TRAVAIL</div>
    
    <div class="content">
        ${attestation.contenu.replace(/\n/g, '<br>')}
    </div>
    
    <div class="footer">
        <div class="date">
            Généré le ${new Date().toLocaleDateString('fr-FR')}
        </div>
        <div class="signature">
            <p>Signature et cachet de l'entreprise</p>
            <p><strong>Direction des Ressources Humaines</strong></p>
        </div>
    </div>
</body>
</html>`

    // Pour une vraie application, vous utiliseriez une bibliothèque comme puppeteer
    // Pour l'instant, on retourne le HTML qui peut être converti en PDF côté client
    return new NextResponse(htmlContent, {
      headers: {
        'Content-Type': 'text/html',
        'Content-Disposition': `attachment; filename="attestation-${attestation.demande?.user?.name || 'document'}.html"`,
      },
    })

  } catch (error) {
    console.error('Erreur génération PDF:', error)
    return NextResponse.json(
      { error: 'Erreur lors de la génération du PDF' },
      { status: 500 }
    )
  }
}
