"use client"

import { useSearchParams } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, ArrowLeft } from "lucide-react"
import Link from "next/link"

export default function AuthErrorPage() {
  const searchParams = useSearchParams()
  const error = searchParams.get('error')

  const getErrorMessage = (error: string | null) => {
    switch (error) {
      case 'CredentialsSignin':
        return 'Email ou mot de passe incorrect. Veuillez vérifier vos identifiants.'
      case 'Configuration':
        return 'Erreur de configuration du serveur. Contactez l\'administrateur.'
      case 'AccessDenied':
        return 'Accès refusé. Vous n\'avez pas les permissions nécessaires.'
      case 'Verification':
        return 'Erreur de vérification. Le lien peut avoir expiré.'
      default:
        return 'Une erreur inattendue s\'est produite lors de la connexion.'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-red-100 flex items-center justify-center">
            <AlertCircle className="h-6 w-6 text-red-600" />
          </div>
          <CardTitle className="text-xl text-red-900">Erreur de connexion</CardTitle>
          <CardDescription>
            Un problème est survenu lors de votre tentative de connexion
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {getErrorMessage(error)}
            </AlertDescription>
          </Alert>

          <div className="space-y-2">
            <Link href="/auth/signin">
              <Button className="w-full" variant="default">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Retour à la connexion
              </Button>
            </Link>
          </div>

          <div className="text-center text-sm text-gray-600">
            <p>Si le problème persiste, contactez l'administrateur système.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
