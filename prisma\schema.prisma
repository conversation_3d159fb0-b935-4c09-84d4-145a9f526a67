generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Modèle utilisateur (employés + admins)
model User {
  id              String   @id @default(cuid())
  email           String   @unique
  emailVerified   DateTime?
  name            String?  // Garder 'name' pour compatibilité
  nom             String?
  prenom          String?
  poste           String?
  departement     String?
  manager         String?
  telephone       String?
  adresse         String?
  dateEmbauche    DateTime?
  salaire         Float?
  congesRestants  Int      @default(25)
  role            String   @default("EMPLOYE") // Rôles: EMPLOYE, MANAGER, RH, ADMIN
  statut          String   @default("ACTIF")
  avatar          String?
  image           String?  // Pour NextAuth.js
  password        String?  // Hash du mot de passe
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations NextAuth.js
  accounts        Account[]
  sessions        Session[]

  // Relations métier
  demandes        Demande[]
  notifications   Notification[]

  @@map("users")
}

// Tables NextAuth.js
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

// Modèle des demandes
model Demande {
  id              String   @id @default(cuid())
  type            String
  message         String?  // Garder pour compatibilité
  status          String   @default("pending") // Garder pour compatibilité
  statut          String   @default("EN_ATTENTE")
  priorite        String   @default("NORMALE")

  // Détails de la demande
  titre           String?
  description     String?
  motif           String?

  // Dates spécifiques aux congés
  dateDebut       DateTime?
  dateFin         DateTime?

  // Détails spécifiques au matériel
  materiel        String?
  quantite        Int?
  budgetEstime    Float?
  justification   String?

  // Détails spécifiques aux attestations
  typeAttestation String?
  motifAttestation String?
  langue          String   @default("français")

  // Traitement
  dateTraitement  DateTime?
  motifRejet      String?
  commentaires    String?

  // Métadonnées
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  userId          String
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  attestation     Attestation?
  notifications   Notification[]

  @@map("demandes")
}

// Modèle des attestations
model Attestation {
  id              String   @id @default(cuid())
  contenu         String
  statut          String   @default("GENEREE")

  // Métadonnées de génération
  genereParIA     Boolean  @default(true)
  modeleUtilise   String?

  // Envoi
  dateEnvoi       DateTime?
  emailEnvoye     Boolean  @default(false)

  // Métadonnées
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  demandeId       String   @unique
  demande         Demande  @relation(fields: [demandeId], references: [id], onDelete: Cascade)

  @@map("attestations")
}

// Modèle des notifications
model Notification {
  id              String   @id @default(cuid())
  titre           String
  message         String
  type            String   @default("INFO")
  lu              Boolean  @default(false)
  dateLecture     DateTime?

  // Métadonnées
  createdAt       DateTime @default(now())

  // Relations
  userId          String?
  user            User?    @relation(fields: [userId], references: [id], onDelete: Cascade)
  demandeId       String?
  demande         Demande? @relation(fields: [demandeId], references: [id], onDelete: Cascade)

  @@map("notifications")
}
