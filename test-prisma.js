const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function test() {
  try {
    console.log('Prisma client:', Object.keys(prisma))
    console.log('<PERSON>d<PERSON><PERSON> disponibles:', Object.keys(prisma).filter(key => !key.startsWith('$') && !key.startsWith('_')))
  } catch (error) {
    console.error('Erreur:', error)
  } finally {
    await prisma.$disconnect()
  }
}

test()
