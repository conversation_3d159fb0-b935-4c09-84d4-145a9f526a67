import { auth } from "@/lib/auth"
import { redirect } from "next/navigation"

// Types de rôles
export type Role = "EMPLOYE" | "MANAGER" | "RH" | "ADMIN"

// Obtenir la session utilisateur actuelle
export async function getCurrentUser() {
  try {
    const session = await auth()
    return session?.user || null
  } catch (error) {
    console.error("Erreur lors de la récupération de la session:", error)
    return null
  }
}

// Middleware pour protéger les routes
export async function requireAuth() {
  const user = await getCurrentUser()
  if (!user) {
    redirect("/auth/signin")
  }
  return user
}

// Vérifier si un utilisateur a un rôle spécifique
export function hasRole(userRole: string, requiredRole: Role): boolean {
  return userRole === requiredRole || userRole === "ADMIN"
}

// Obtenir le nom d'affichage d'un rôle
export function getRoleDisplayName(role: string): string {
  const roleNames: Record<string, string> = {
    EMPLOYE: "Employé",
    MANAGER: "Manager",
    RH: "Ressources Humaines",
    ADMIN: "Administrateur",
  }
  return roleNames[role] || role
}
