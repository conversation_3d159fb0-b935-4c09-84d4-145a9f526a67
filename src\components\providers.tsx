"use client"

import { SessionProvider } from "next-auth/react"
import { ThemeProvider } from "next-themes"
import { Toaster } from "@/components/ui/sonner"

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <SessionProvider>
      <ThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        disableTransitionOnChange
        storageKey="rh-manager-theme"
      >
        <div suppressHydrationWarning>
          {children}
        </div>
        <Toaster />
      </ThemeProvider>
    </SessionProvider>
  )
}
