import { IconTrendingDown, IconT<PERSON>dingUp, Icon<PERSON>lock, IconCheck, IconX, IconUsers } from "@tabler/icons-react"

import { Badge } from "@/components/ui/badge"
import {
  Card,
  CardAction,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

interface SectionCardsProps {
  statistics: {
    totalDemandes: number
    demandesEnAttente: number
    demandesValidees: number
    demandesRejetees: number
    demandesEnCours: number
    totalUsers: number
    tauxTraitement: number
  }
}

export function SectionCards({ statistics }: SectionCardsProps) {
  const {
    totalDemandes,
    demandesEnAttente,
    demandesValidees,
    demandesRejetees,
    demandesEnCours,
    totalUsers,
    tauxTraitement,
  } = statistics
  return (
    <div className="*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 px-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs lg:px-6 @xl/main:grid-cols-2 @5xl/main:grid-cols-4">
      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Demandes en attente</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
            {demandesEnAttente}
          </CardTitle>
          <CardAction>
            <Badge variant="outline" className="text-orange-600">
              <IconClock />
              En attente
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            Nécessitent une action <IconClock className="size-4" />
          </div>
          <div className="text-muted-foreground">
            Sur {totalDemandes} demandes au total
          </div>
        </CardFooter>
      </Card>

      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Demandes validées</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
            {demandesValidees}
          </CardTitle>
          <CardAction>
            <Badge variant="outline" className="text-green-600">
              <IconCheck />
              Validées
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            Traitées avec succès <IconCheck className="size-4" />
          </div>
          <div className="text-muted-foreground">
            {demandesRejetees} rejetée{demandesRejetees > 1 ? 's' : ''}, {demandesEnCours} en cours
          </div>
        </CardFooter>
      </Card>

      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Total employés</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
            {totalUsers}
          </CardTitle>
          <CardAction>
            <Badge variant="outline">
              <IconUsers />
              Employés
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            Équipe complète <IconUsers className="size-4" />
          </div>
          <div className="text-muted-foreground">
            Employés enregistrés dans le système
          </div>
        </CardFooter>
      </Card>

      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Taux de traitement</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
            {tauxTraitement}%
          </CardTitle>
          <CardAction>
            <Badge variant="outline" className={tauxTraitement >= 70 ? "text-green-600" : "text-orange-600"}>
              {tauxTraitement >= 70 ? <IconTrendingUp /> : <IconTrendingDown />}
              {tauxTraitement >= 70 ? "Bon" : "À améliorer"}
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            Demandes traitées <IconCheck className="size-4" />
          </div>
          <div className="text-muted-foreground">
            {demandesValidees + demandesRejetees} sur {totalDemandes} demandes traitées
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}
