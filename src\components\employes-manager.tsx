"use client"

import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  Users, 
  Plus, 
  Search, 
  Mail, 
  Phone, 
  Building, 
  Calendar,
  Edit,
  Eye,
  MoreHorizontal,
  UserPlus
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { createUser, updateUser } from "@/lib/actions"
import { toast } from "sonner"

interface EmployesManagerProps {
  users: any[]
}

export function EmployesManager({ users }: EmployesManagerProps) {
  const [searchTerm, setSearchTerm] = React.useState("")
  const [selectedDepartement, setSelectedDepartement] = React.useState("all")
  const [isCreating, setIsCreating] = React.useState(false)
  const [isLoading, setIsLoading] = React.useState(false)
  const [newUser, setNewUser] = React.useState({
    email: "",
    name: "",
    nom: "",
    prenom: "",
    poste: "",
    departement: "",
    role: "user",
  })

  // Filtrer les utilisateurs
  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.poste?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesDepartement = selectedDepartement === "all" || 
                              user.departement === selectedDepartement

    return matchesSearch && matchesDepartement
  })

  // Obtenir la liste des départements uniques
  const departements = [...new Set(users.map(u => u.departement).filter(Boolean))]

  // Statistiques
  const stats = {
    total: users.length,
    actifs: users.filter(u => u.statut === 'ACTIF' || !u.statut).length,
    parDepartement: departements.map(dept => ({
      nom: dept,
      count: users.filter(u => u.departement === dept).length
    }))
  }

  const handleCreateUser = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!newUser.email || !newUser.name) {
      toast.error('Email et nom sont obligatoires')
      return
    }

    setIsLoading(true)
    try {
      const result = await createUser(newUser)
      if (result.success) {
        toast.success('Employé créé avec succès')
        setNewUser({
          email: "",
          name: "",
          nom: "",
          prenom: "",
          poste: "",
          departement: "",
          role: "user",
        })
        setIsCreating(false)
      } else {
        toast.error(result.error || 'Erreur lors de la création')
      }
    } catch (error) {
      toast.error('Erreur lors de la création')
    } finally {
      setIsLoading(false)
    }
  }

  const getRoleBadge = (role: string) => {
    switch (role) {
      case "admin":
        return <Badge variant="destructive">Admin</Badge>
      case "RH":
        return <Badge variant="default">RH</Badge>
      case "manager":
        return <Badge variant="secondary">Manager</Badge>
      default:
        return <Badge variant="outline">Employé</Badge>
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  return (
    <div className="space-y-6">
      {/* Statistiques */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Employés</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              employés enregistrés
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Actifs</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.actifs}</div>
            <p className="text-xs text-muted-foreground">
              employés actifs
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Départements</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{departements.length}</div>
            <p className="text-xs text-muted-foreground">
              départements actifs
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Demandes</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {users.reduce((total, user) => total + (user.demandes?.length || 0), 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              demandes totales
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="liste" className="w-full">
        <TabsList>
          <TabsTrigger value="liste">Liste des employés</TabsTrigger>
          <TabsTrigger value="ajouter">Ajouter un employé</TabsTrigger>
          <TabsTrigger value="departements">Par département</TabsTrigger>
        </TabsList>
        
        <TabsContent value="liste">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Liste des Employés</CardTitle>
                  <CardDescription>
                    Gérez tous les employés de l'entreprise
                  </CardDescription>
                </div>
                <Button onClick={() => setIsCreating(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Nouvel employé
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {/* Filtres */}
              <div className="flex gap-4 mb-6">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Rechercher par nom, email ou poste..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-8"
                    />
                  </div>
                </div>
                <Select value={selectedDepartement} onValueChange={setSelectedDepartement}>
                  <SelectTrigger className="w-[200px]">
                    <SelectValue placeholder="Département" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tous les départements</SelectItem>
                    {departements.map((dept) => (
                      <SelectItem key={dept} value={dept}>
                        {dept}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Liste des employés */}
              <div className="space-y-4">
                {filteredUsers.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    Aucun employé trouvé
                  </div>
                ) : (
                  filteredUsers.map((user) => (
                    <div key={user.id} className="border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <Avatar className="h-12 w-12">
                            <AvatarImage src={user.avatar} />
                            <AvatarFallback>{getInitials(user.name || 'UN')}</AvatarFallback>
                          </Avatar>
                          
                          <div className="space-y-1">
                            <div className="flex items-center gap-2">
                              <h4 className="font-medium">{user.name}</h4>
                              {getRoleBadge(user.role)}
                            </div>
                            <div className="flex items-center gap-4 text-sm text-muted-foreground">
                              <span className="flex items-center gap-1">
                                <Mail className="h-3 w-3" />
                                {user.email}
                              </span>
                              {user.poste && (
                                <span>{user.poste}</span>
                              )}
                              {user.departement && (
                                <span className="flex items-center gap-1">
                                  <Building className="h-3 w-3" />
                                  {user.departement}
                                </span>
                              )}
                              {user.dateEmbauche && (
                                <span className="flex items-center gap-1">
                                  <Calendar className="h-3 w-3" />
                                  {new Date(user.dateEmbauche).toLocaleDateString('fr-FR')}
                                </span>
                              )}
                            </div>
                            {user.demandes && user.demandes.length > 0 && (
                              <div className="text-xs text-muted-foreground">
                                {user.demandes.length} demande{user.demandes.length > 1 ? 's' : ''}
                              </div>
                            )}
                          </div>
                        </div>
                        
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              Voir le profil
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              Modifier
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>
                              Voir les demandes
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="ajouter">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <UserPlus className="h-5 w-5" />
                Ajouter un nouvel employé
              </CardTitle>
              <CardDescription>
                Créez un nouveau compte employé dans le système
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleCreateUser} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={newUser.email}
                      onChange={(e) => setNewUser(prev => ({ ...prev, email: e.target.value }))}
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="name">Nom complet *</Label>
                    <Input
                      id="name"
                      value={newUser.name}
                      onChange={(e) => setNewUser(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Prénom Nom"
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="poste">Poste</Label>
                    <Input
                      id="poste"
                      value={newUser.poste}
                      onChange={(e) => setNewUser(prev => ({ ...prev, poste: e.target.value }))}
                      placeholder="Développeur, Manager, etc."
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="departement">Département</Label>
                    <Select
                      value={newUser.departement}
                      onValueChange={(value) => setNewUser(prev => ({ ...prev, departement: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Sélectionner un département" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="IT">IT</SelectItem>
                        <SelectItem value="Finance">Finance</SelectItem>
                        <SelectItem value="RH">RH</SelectItem>
                        <SelectItem value="Marketing">Marketing</SelectItem>
                        <SelectItem value="Commercial">Commercial</SelectItem>
                        <SelectItem value="Production">Production</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="role">Rôle</Label>
                    <Select
                      value={newUser.role}
                      onValueChange={(value) => setNewUser(prev => ({ ...prev, role: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Sélectionner un rôle" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="user">Employé</SelectItem>
                        <SelectItem value="manager">Manager</SelectItem>
                        <SelectItem value="RH">RH</SelectItem>
                        <SelectItem value="admin">Admin</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button type="submit" disabled={isLoading}>
                    {isLoading ? 'Création...' : 'Créer l\'employé'}
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => {
                      setIsCreating(false)
                      setNewUser({
                        email: "",
                        name: "",
                        nom: "",
                        prenom: "",
                        poste: "",
                        departement: "",
                        role: "user",
                      })
                    }}
                  >
                    Annuler
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="departements">
          <Card>
            <CardHeader>
              <CardTitle>Répartition par département</CardTitle>
              <CardDescription>
                Vue d'ensemble des employés par département
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats.parDepartement.map((dept) => (
                  <div key={dept.nom} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <Building className="h-5 w-5 text-muted-foreground" />
                      <div>
                        <h4 className="font-medium">{dept.nom}</h4>
                        <p className="text-sm text-muted-foreground">
                          {dept.count} employé{dept.count > 1 ? 's' : ''}
                        </p>
                      </div>
                    </div>
                    <Badge variant="outline">{dept.count}</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
