import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

const VALID_ROLES = ["EMPLOYE", "MANAGER", "RH", "ADMIN"]

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  console.log(`🔧 API: Tentative de modification du rôle pour l'utilisateur ${params.id}`)

  try {
    // Vérifier l'authentification
    const session = await auth()
    console.log(`👤 Session:`, session?.user?.email, session?.user?.role)

    if (!session?.user) {
      console.log(`❌ Non authentifié`)
      return NextResponse.json(
        { message: "Non authentifié" },
        { status: 401 }
      )
    }

    // Vérifier que l'utilisateur est admin
    if (session.user.role !== "ADMIN") {
      console.log(`❌ Accès refusé: ${session.user.role} n'est pas ADMIN`)
      return NextResponse.json(
        { message: "Accès refusé. Seuls les administrateurs peuvent modifier les rôles." },
        { status: 403 }
      )
    }

    // Récupérer les données de la requête
    const body = await request.json()
    const { role } = body
    console.log(`📝 Nouveau rôle demandé: ${role}`)

    // Valider le rôle
    if (!role || !VALID_ROLES.includes(role)) {
      console.log(`❌ Rôle invalide: ${role}`)
      return NextResponse.json(
        { message: `Rôle invalide: ${role}. Rôles valides: ${VALID_ROLES.join(', ')}` },
        { status: 400 }
      )
    }

    // Vérifier que l'utilisateur à modifier existe
    const targetUser = await prisma.user.findUnique({
      where: { id: params.id }
    })

    if (!targetUser) {
      console.log(`❌ Utilisateur non trouvé: ${params.id}`)
      return NextResponse.json(
        { message: "Utilisateur non trouvé" },
        { status: 404 }
      )
    }

    console.log(`👤 Utilisateur cible: ${targetUser.email} (rôle actuel: ${targetUser.role})`)

    // Empêcher un admin de modifier son propre rôle
    if (targetUser.id === session.user.id) {
      console.log(`❌ Tentative d'auto-modification`)
      return NextResponse.json(
        { message: "Vous ne pouvez pas modifier votre propre rôle" },
        { status: 400 }
      )
    }

    // Mettre à jour le rôle
    console.log(`🔄 Mise à jour en cours...`)
    const updatedUser = await prisma.user.update({
      where: { id: params.id },
      data: { role },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
      }
    })

    console.log(`✅ Rôle mis à jour avec succès: ${updatedUser.email} → ${role} (par ${session.user.email})`)

    return NextResponse.json({
      message: "Rôle mis à jour avec succès",
      user: updatedUser
    })

  } catch (error) {
    console.error("❌ Erreur lors de la mise à jour du rôle:", error)
    return NextResponse.json(
      { message: "Erreur serveur" },
      { status: 500 }
    )
  }
}
