import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    // Vérifier l'authentification
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json(
        { message: "Non authentifié" },
        { status: 401 }
      )
    }

    // Vérifier que l'utilisateur est admin
    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { message: "Accès refusé. Seuls les administrateurs peuvent voir cette liste." },
        { status: 403 }
      )
    }

    // Récupérer tous les utilisateurs
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        prenom: true,
        nom: true,
        role: true,
        statut: true,
        createdAt: true,
      },
      orderBy: [
        { role: "desc" }, // Admins en premier
        { createdAt: "desc" }
      ]
    })

    return NextResponse.json(users)

  } catch (error) {
    console.error("Erreur lors de la récupération des utilisateurs:", error)
    return NextResponse.json(
      { message: "Erreur serveur" },
      { status: 500 }
    )
  }
}
