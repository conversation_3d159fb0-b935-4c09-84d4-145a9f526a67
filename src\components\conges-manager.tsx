"use client"

import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Calendar,
  User,
  Clock,
  CheckCircle,
  XCircle,
  Trash2,
  Eye,
  Filter,
  Download,
  AlertCircle,
  CalendarDays,
  Building,
  Mail
} from "lucide-react"
import { updateDemandeStatus, deleteDemande } from "@/lib/actions"
import { toast } from "sonner"

interface CongesManagerProps {
  demandes: any[]
  users: any[]
}

export function CongesManager({ demandes, users }: CongesManagerProps) {
  const [isLoading, setIsLoading] = React.useState(false)
  const [filtreStatut, setFiltreStatut] = React.useState<string>("tous")
  const [filtreDepartement, setFiltreDepartement] = React.useState<string>("tous")
  const [selectedDemande, setSelectedDemande] = React.useState<any>(null)
  const [motifRejet, setMotifRejet] = React.useState("")
  const [commentaires, setCommentaires] = React.useState("")
  const [showRejectDialog, setShowRejectDialog] = React.useState(false)
  const [showDetailsDialog, setShowDetailsDialog] = React.useState(false)
  const [selectedDemandes, setSelectedDemandes] = React.useState<string[]>([])
  const [showBulkActions, setShowBulkActions] = React.useState(false)

  // Filtrer les demandes selon les critères
  const demandesFiltrees = React.useMemo(() => {
    return demandes.filter(demande => {
      const matchStatut = filtreStatut === "tous" || demande.statut === filtreStatut
      const matchDepartement = filtreDepartement === "tous" || demande.user?.departement === filtreDepartement
      return matchStatut && matchDepartement
    })
  }, [demandes, filtreStatut, filtreDepartement])

  // Obtenir les départements uniques
  const departements = React.useMemo(() => {
    const depts = [...new Set(users.map(user => user.departement).filter(Boolean))]
    return depts
  }, [users])

  // Calculer les statistiques
  const statistiques = React.useMemo(() => {
    const total = demandes.length
    const enAttente = demandes.filter(d => d.statut === 'EN_ATTENTE').length
    const validees = demandes.filter(d => d.statut === 'VALIDEE').length
    const rejetees = demandes.filter(d => d.statut === 'REJETEE').length

    return { total, enAttente, validees, rejetees }
  }, [demandes])

  const getStatutBadge = (statut: string) => {
    switch (statut) {
      case "EN_ATTENTE":
        return <Badge variant="outline" className="text-orange-600 border-orange-200">En attente</Badge>
      case "VALIDEE":
        return <Badge variant="outline" className="text-green-600 border-green-200">Validée</Badge>
      case "REJETEE":
        return <Badge variant="outline" className="text-red-600 border-red-200">Rejetée</Badge>
      case "EN_COURS":
        return <Badge variant="outline" className="text-blue-600 border-blue-200">En cours</Badge>
      case "ANNULEE":
        return <Badge variant="outline" className="text-gray-600 border-gray-200">Annulée</Badge>
      default:
        return <Badge variant="outline">{statut}</Badge>
    }
  }

  const getPrioriteBadge = (priorite: string) => {
    switch (priorite) {
      case "URGENTE":
        return <Badge variant="destructive">Urgente</Badge>
      case "HAUTE":
        return <Badge variant="secondary" className="bg-orange-100 text-orange-800">Haute</Badge>
      case "NORMALE":
        return <Badge variant="outline">Normale</Badge>
      case "BASSE":
        return <Badge variant="outline" className="text-gray-600">Basse</Badge>
      default:
        return <Badge variant="outline">{priorite}</Badge>
    }
  }

  const calculerDureeConge = (dateDebut: string, dateFin: string) => {
    const debut = new Date(dateDebut)
    const fin = new Date(dateFin)
    const diffTime = Math.abs(fin.getTime() - debut.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1
    return diffDays
  }

  const validerConge = async (demandeId: string, commentairePersonnalise?: string) => {
    setIsLoading(true)
    try {
      const demande = demandes.find(d => d.id === demandeId)
      const commentaireValidation = commentairePersonnalise ||
        commentaires ||
        `Congé validé le ${new Date().toLocaleDateString('fr-FR')} - Approuvé par RH`

      const result = await updateDemandeStatus(demandeId, 'VALIDEE', commentaireValidation)
      if (result.success) {
        toast.success(`Congé de ${demande?.user?.name || 'l\'employé'} validé avec succès`)
        setCommentaires("")
        window.location.reload()
      } else {
        toast.error(result.error || 'Erreur lors de la validation')
      }
    } catch (error) {
      toast.error('Erreur lors de la validation')
    } finally {
      setIsLoading(false)
    }
  }

  const rejeterConge = async () => {
    if (!selectedDemande || !motifRejet.trim()) {
      toast.error('Veuillez saisir un motif de rejet')
      return
    }

    setIsLoading(true)
    try {
      const result = await updateDemandeStatus(selectedDemande.id, 'REJETEE', motifRejet)
      if (result.success) {
        toast.success('Congé rejeté avec succès')
        setShowRejectDialog(false)
        setMotifRejet("")
        setSelectedDemande(null)
        window.location.reload()
      } else {
        toast.error(result.error || 'Erreur lors du rejet')
      }
    } catch (error) {
      toast.error('Erreur lors du rejet')
    } finally {
      setIsLoading(false)
    }
  }

  const supprimerConge = async (demandeId: string, nomEmploye: string) => {
    if (!confirm(`Êtes-vous sûr de vouloir supprimer définitivement la demande de congé de ${nomEmploye} ?`)) {
      return
    }

    setIsLoading(true)
    try {
      const result = await deleteDemande(demandeId)
      if (result.success) {
        toast.success('Demande de congé supprimée avec succès')
        window.location.reload()
      } else {
        toast.error(result.error || 'Erreur lors de la suppression')
      }
    } catch (error) {
      toast.error('Erreur lors de la suppression')
    } finally {
      setIsLoading(false)
    }
  }

  const ouvrirDetails = (demande: any) => {
    setSelectedDemande(demande)
    setShowDetailsDialog(true)
  }

  const ouvrirRejet = (demande: any) => {
    setSelectedDemande(demande)
    setShowRejectDialog(true)
  }

  return (
    <div className="space-y-6">
      {/* Statistiques */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total</p>
                <p className="text-2xl font-bold">{statistiques.total}</p>
              </div>
              <CalendarDays className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">En attente</p>
                <p className="text-2xl font-bold text-orange-600">{statistiques.enAttente}</p>
              </div>
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Validées</p>
                <p className="text-2xl font-bold text-green-600">{statistiques.validees}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Rejetées</p>
                <p className="text-2xl font-bold text-red-600">{statistiques.rejetees}</p>
              </div>
              <XCircle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filtres */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtres
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <Label>Statut</Label>
              <Select value={filtreStatut} onValueChange={setFiltreStatut}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="tous">Tous les statuts</SelectItem>
                  <SelectItem value="EN_ATTENTE">En attente</SelectItem>
                  <SelectItem value="VALIDEE">Validées</SelectItem>
                  <SelectItem value="REJETEE">Rejetées</SelectItem>
                  <SelectItem value="EN_COURS">En cours</SelectItem>
                  <SelectItem value="ANNULEE">Annulées</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Département</Label>
              <Select value={filtreDepartement} onValueChange={setFiltreDepartement}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="tous">Tous les départements</SelectItem>
                  {departements.map(dept => (
                    <SelectItem key={dept} value={dept}>{dept}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-end">
              <Button
                variant="outline"
                onClick={() => {
                  setFiltreStatut("tous")
                  setFiltreDepartement("tous")
                }}
              >
                Réinitialiser
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Actions rapides */}
      {demandesFiltrees.filter(d => d.statut === 'EN_ATTENTE').length > 0 && (
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5 text-orange-600" />
                <div>
                  <h3 className="font-medium text-orange-800">
                    {demandesFiltrees.filter(d => d.statut === 'EN_ATTENTE').length} demande(s) en attente
                  </h3>
                  <p className="text-sm text-orange-600">
                    Ces demandes nécessitent votre attention
                  </p>
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  className="bg-green-600 hover:bg-green-700 text-white"
                  onClick={() => {
                    const demandesEnAttente = demandesFiltrees.filter(d => d.statut === 'EN_ATTENTE')
                    if (demandesEnAttente.length > 0) {
                      validerConge(demandesEnAttente[0].id)
                    }
                  }}
                  disabled={isLoading}
                >
                  <CheckCircle className="h-4 w-4 mr-1" />
                  Valider la première
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setFiltreStatut("EN_ATTENTE")}
                >
                  <Filter className="h-4 w-4 mr-1" />
                  Voir toutes
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Table des congés */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Demandes de congés ({demandesFiltrees.length})
            </div>
            <div className="flex gap-2">
              {filtreStatut === "EN_ATTENTE" && demandesFiltrees.length > 0 && (
                <>
                  <Button
                    size="sm"
                    className="bg-green-600 hover:bg-green-700 text-white"
                    onClick={() => {
                      const premiereDemande = demandesFiltrees[0]
                      if (premiereDemande) {
                        validerConge(premiereDemande.id)
                      }
                    }}
                    disabled={isLoading}
                  >
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Valider tout
                  </Button>
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => {
                      const premiereDemande = demandesFiltrees[0]
                      if (premiereDemande) {
                        ouvrirRejet(premiereDemande)
                      }
                    }}
                    disabled={isLoading}
                  >
                    <XCircle className="mr-2 h-4 w-4" />
                    Refuser tout
                  </Button>
                </>
              )}
              <Button variant="outline" size="sm">
                <Download className="mr-2 h-4 w-4" />
                Exporter
              </Button>
            </div>
          </CardTitle>
          <CardDescription>
            Gérez toutes les demandes de congés des employés - Validez ou refusez directement
          </CardDescription>
        </CardHeader>
        <CardContent>
          {demandesFiltrees.length === 0 ? (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Aucune demande de congé trouvée avec les filtres actuels.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Employé</TableHead>
                    <TableHead>Département</TableHead>
                    <TableHead>Dates</TableHead>
                    <TableHead>Durée</TableHead>
                    <TableHead>Motif</TableHead>
                    <TableHead>Statut</TableHead>
                    <TableHead>Priorité</TableHead>
                    <TableHead>Demandé le</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {demandesFiltrees.map((demande) => (
                    <TableRow key={demande.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <div className="font-medium">{demande.user?.name || 'Nom inconnu'}</div>
                            <div className="text-sm text-muted-foreground">{demande.user?.email}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Building className="h-4 w-4 text-muted-foreground" />
                          {demande.user?.departement || 'Non spécifié'}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="text-sm">
                            <strong>Du:</strong> {demande.dateDebut ? new Date(demande.dateDebut).toLocaleDateString('fr-FR') : 'Non spécifié'}
                          </div>
                          <div className="text-sm">
                            <strong>Au:</strong> {demande.dateFin ? new Date(demande.dateFin).toLocaleDateString('fr-FR') : 'Non spécifié'}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {demande.dateDebut && demande.dateFin ? (
                          <Badge variant="outline">
                            {calculerDureeConge(demande.dateDebut, demande.dateFin)} jour(s)
                          </Badge>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="max-w-[200px] truncate" title={demande.motif || demande.description}>
                          {demande.motif || demande.description || 'Aucun motif'}
                        </div>
                      </TableCell>
                      <TableCell>{getStatutBadge(demande.statut)}</TableCell>
                      <TableCell>{getPrioriteBadge(demande.priorite)}</TableCell>
                      <TableCell>
                        {new Date(demande.createdAt).toLocaleDateString('fr-FR')}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-1">
                          {/* Actions pour demandes en attente */}
                          {demande.statut === 'EN_ATTENTE' && (
                            <>
                              <Button
                                size="sm"
                                className="bg-green-600 hover:bg-green-700 text-white"
                                onClick={() => {
                                  if (confirm(`Êtes-vous sûr de vouloir valider le congé de ${demande.user?.name} du ${demande.dateDebut ? new Date(demande.dateDebut).toLocaleDateString('fr-FR') : 'date inconnue'} au ${demande.dateFin ? new Date(demande.dateFin).toLocaleDateString('fr-FR') : 'date inconnue'} ?`)) {
                                    validerConge(demande.id)
                                  }
                                }}
                                disabled={isLoading}
                              >
                                <CheckCircle className="h-4 w-4 mr-1" />
                                Valider
                              </Button>
                              <Button
                                size="sm"
                                variant="destructive"
                                onClick={() => ouvrirRejet(demande)}
                                disabled={isLoading}
                              >
                                <XCircle className="h-4 w-4 mr-1" />
                                Refuser
                              </Button>
                            </>
                          )}

                          {/* Actions pour toutes les demandes */}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => ouvrirDetails(demande)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>

                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-red-600 hover:text-red-700"
                            onClick={() => supprimerConge(demande.id, demande.user?.name || 'Employé')}
                            disabled={isLoading}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Dialog de détails */}
      <Dialog open={showDetailsDialog} onOpenChange={setShowDetailsDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Détails de la demande de congé
            </DialogTitle>
            <DialogDescription>
              Informations complètes sur la demande de congé
            </DialogDescription>
          </DialogHeader>

          {selectedDemande && (
            <div className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label>Employé</Label>
                  <div className="flex items-center gap-2 p-2 border rounded">
                    <User className="h-4 w-4" />
                    <div>
                      <div className="font-medium">{selectedDemande.user?.name}</div>
                      <div className="text-sm text-muted-foreground">{selectedDemande.user?.email}</div>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Département</Label>
                  <div className="flex items-center gap-2 p-2 border rounded">
                    <Building className="h-4 w-4" />
                    {selectedDemande.user?.departement || 'Non spécifié'}
                  </div>
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label>Date de début</Label>
                  <div className="p-2 border rounded">
                    {selectedDemande.dateDebut ? new Date(selectedDemande.dateDebut).toLocaleDateString('fr-FR') : 'Non spécifiée'}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Date de fin</Label>
                  <div className="p-2 border rounded">
                    {selectedDemande.dateFin ? new Date(selectedDemande.dateFin).toLocaleDateString('fr-FR') : 'Non spécifiée'}
                  </div>
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <Label>Durée</Label>
                  <div className="p-2 border rounded">
                    {selectedDemande.dateDebut && selectedDemande.dateFin ?
                      `${calculerDureeConge(selectedDemande.dateDebut, selectedDemande.dateFin)} jour(s)` :
                      'Non calculable'
                    }
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Statut</Label>
                  <div className="p-2 border rounded">
                    {getStatutBadge(selectedDemande.statut)}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Priorité</Label>
                  <div className="p-2 border rounded">
                    {getPrioriteBadge(selectedDemande.priorite)}
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Motif de la demande</Label>
                <div className="p-3 border rounded bg-muted/50">
                  {selectedDemande.motif || selectedDemande.description || 'Aucun motif spécifié'}
                </div>
              </div>

              {selectedDemande.commentaires && (
                <div className="space-y-2">
                  <Label>Commentaires</Label>
                  <div className="p-3 border rounded bg-muted/50">
                    {selectedDemande.commentaires}
                  </div>
                </div>
              )}

              {selectedDemande.motifRejet && (
                <div className="space-y-2">
                  <Label>Motif de rejet</Label>
                  <div className="p-3 border rounded bg-red-50 text-red-800">
                    {selectedDemande.motifRejet}
                  </div>
                </div>
              )}

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label>Demandé le</Label>
                  <div className="p-2 border rounded">
                    {new Date(selectedDemande.createdAt).toLocaleDateString('fr-FR')} à {new Date(selectedDemande.createdAt).toLocaleTimeString('fr-FR')}
                  </div>
                </div>

                {selectedDemande.dateTraitement && (
                  <div className="space-y-2">
                    <Label>Traité le</Label>
                    <div className="p-2 border rounded">
                      {new Date(selectedDemande.dateTraitement).toLocaleDateString('fr-FR')} à {new Date(selectedDemande.dateTraitement).toLocaleTimeString('fr-FR')}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDetailsDialog(false)}>
              Fermer
            </Button>
            {selectedDemande?.statut === 'EN_ATTENTE' && (
              <>
                <Button
                  variant="outline"
                  className="text-green-600 border-green-200 hover:bg-green-50"
                  onClick={() => {
                    setShowDetailsDialog(false)
                    validerConge(selectedDemande.id)
                  }}
                  disabled={isLoading}
                >
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Valider
                </Button>
                <Button
                  variant="outline"
                  className="text-red-600 border-red-200 hover:bg-red-50"
                  onClick={() => {
                    setShowDetailsDialog(false)
                    ouvrirRejet(selectedDemande)
                  }}
                  disabled={isLoading}
                >
                  <XCircle className="mr-2 h-4 w-4" />
                  Rejeter
                </Button>
              </>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog de rejet */}
      <Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <XCircle className="h-5 w-5 text-red-600" />
              Rejeter la demande de congé
            </DialogTitle>
            <DialogDescription>
              Veuillez indiquer le motif du rejet de cette demande de congé
            </DialogDescription>
          </DialogHeader>

          {selectedDemande && (
            <div className="space-y-4">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Demande de :</strong> {selectedDemande.user?.name}<br />
                  <strong>Période :</strong> {selectedDemande.dateDebut ? new Date(selectedDemande.dateDebut).toLocaleDateString('fr-FR') : 'Non spécifiée'} - {selectedDemande.dateFin ? new Date(selectedDemande.dateFin).toLocaleDateString('fr-FR') : 'Non spécifiée'}
                </AlertDescription>
              </Alert>

              <div className="space-y-2">
                <Label htmlFor="motifRejet">Motif du rejet *</Label>
                <Textarea
                  id="motifRejet"
                  placeholder="Expliquez pourquoi cette demande de congé est rejetée..."
                  value={motifRejet}
                  onChange={(e) => setMotifRejet(e.target.value)}
                  rows={4}
                />
                <p className="text-xs text-muted-foreground">
                  Ce motif sera communiqué à l'employé
                </p>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowRejectDialog(false)
                setMotifRejet("")
                setSelectedDemande(null)
              }}
            >
              Annuler
            </Button>
            <Button
              variant="destructive"
              onClick={rejeterConge}
              disabled={isLoading || !motifRejet.trim()}
            >
              {isLoading ? 'Rejet en cours...' : 'Confirmer le rejet'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}