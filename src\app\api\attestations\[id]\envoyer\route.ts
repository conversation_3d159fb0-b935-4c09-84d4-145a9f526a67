import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { requireAuth } from '@/lib/auth-utils'
import { revalidatePath } from 'next/cache'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Vérifier l'authentification
    const currentUser = await requireAuth()
    if (!currentUser) {
      return NextResponse.json({ error: 'Non autorisé' }, { status: 401 })
    }

    const { id } = params

    // Récupérer l'attestation
    const attestation = await prisma.attestation.findUnique({
      where: { id },
      include: {
        demande: {
          include: {
            user: true,
          },
        },
      },
    })

    if (!attestation) {
      return NextResponse.json({ error: 'Attestation non trouvée' }, { status: 404 })
    }

    // Vérifier que l'attestation n'est pas déjà envoyée
    if (attestation.statut === 'ENVOYEE') {
      return NextResponse.json({ error: 'Attestation déjà envoyée' }, { status: 400 })
    }

    // Simuler l'envoi par email (dans une vraie application, vous utiliseriez un service d'email)
    const userEmail = attestation.demande?.user?.email
    
    if (!userEmail) {
      return NextResponse.json({ error: 'Email de l\'utilisateur non trouvé' }, { status: 400 })
    }

    // Mettre à jour le statut de l'attestation
    const updatedAttestation = await prisma.attestation.update({
      where: { id },
      data: {
        statut: 'ENVOYEE',
        dateEnvoi: new Date(),
      },
      include: {
        demande: {
          include: {
            user: true,
          },
        },
      },
    })

    // Créer une notification pour l'utilisateur
    await prisma.notification.create({
      data: {
        titre: 'Attestation envoyée',
        message: `Votre attestation de travail a été envoyée à votre adresse email ${userEmail}`,
        type: 'SUCCESS',
        userId: attestation.demande?.userId || '',
      },
    })

    // Créer une notification pour les RH
    await prisma.notification.create({
      data: {
        titre: 'Attestation envoyée',
        message: `L'attestation de ${attestation.demande?.user?.name} a été envoyée avec succès`,
        type: 'INFO',
        demandeId: attestation.demandeId,
      },
    })

    // Revalider les pages concernées
    revalidatePath('/dashboard/attestations')
    revalidatePath('/dashboard')

    return NextResponse.json({
      success: true,
      message: 'Attestation envoyée avec succès',
      attestation: updatedAttestation,
    })

  } catch (error) {
    console.error('Erreur envoi attestation:', error)
    return NextResponse.json(
      { error: 'Erreur lors de l\'envoi de l\'attestation' },
      { status: 500 }
    )
  }
}
