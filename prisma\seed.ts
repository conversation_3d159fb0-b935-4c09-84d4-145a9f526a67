import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Début du seeding...')

  // Nettoyer les données existantes (dans l'ordre des dépendances)
  try {
    await prisma.notification.deleteMany()
    await prisma.attestation.deleteMany()
    await prisma.demande.deleteMany()
    await prisma.user.deleteMany()
    console.log('🗑️ Données existantes supprimées')
  } catch (error) {
    console.log('ℹ️ Pas de données existantes à supprimer')
  }

  // Création des utilisateurs (employés)
  console.log('👥 Création des utilisateurs...')
  const users = await Promise.all([
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: '<PERSON>',
        nom: '<PERSON><PERSON>',
        prenom: '<PERSON>',
        poste: 'Développeuse Senior',
        departement: 'IT',
        manager: '<PERSON>',
        telephone: '+33 1 23 45 67 89',
        dateEmbauche: new Date('2020-03-15'),
        salaire: 55000,
        congesRestants: 25,
        role: 'EMPLOYE',
      },
    }),
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Pierre Moreau',
        nom: 'Moreau',
        prenom: 'Pierre',
        poste: 'Comptable',
        departement: 'Finance',
        manager: 'Sophie Laurent',
        telephone: '+33 1 23 45 67 90',
        dateEmbauche: new Date('2019-09-01'),
        salaire: 42000,
        congesRestants: 18,
        role: 'EMPLOYE',
      },
    }),
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Sophie Bernard',
        nom: 'Bernard',
        prenom: 'Sophie',
        poste: 'Responsable RH',
        departement: 'RH',
        manager: 'Directeur Général',
        telephone: '+33 1 23 45 67 91',
        dateEmbauche: new Date('2018-01-10'),
        salaire: 65000,
        congesRestants: 30,
        role: 'RH',
      },
    }),
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Admin Système',
        nom: 'Admin',
        prenom: 'Système',
        poste: 'Administrateur',
        departement: 'IT',
        role: 'ADMIN',
      },
    }),
  ])

  // Création des demandes
  console.log('📋 Création des demandes...')
  const demandes = await Promise.all([
    prisma.demande.create({
      data: {
        type: 'CONGE',
        message: 'Demande de congés pour vacances familiales',
        status: 'pending',
        statut: 'EN_ATTENTE',
        priorite: 'NORMALE',
        titre: 'Demande de congés annuels',
        description: 'Demande de congés pour vacances familiales',
        motif: 'Congés annuels',
        dateDebut: new Date('2024-02-01'),
        dateFin: new Date('2024-02-15'),
        userId: users[0].id,
      },
    }),
    prisma.demande.create({
      data: {
        type: 'MATERIEL',
        message: 'Remplacement ordinateur défaillant',
        status: 'approved',
        statut: 'VALIDEE',
        priorite: 'URGENTE',
        titre: 'Demande ordinateur portable',
        description: 'Remplacement ordinateur défaillant',
        materiel: 'Ordinateur portable',
        quantite: 1,
        budgetEstime: 1200,
        justification: 'Ordinateur actuel en panne',
        dateTraitement: new Date('2024-01-12'),
        userId: users[1].id,
      },
    }),
    prisma.demande.create({
      data: {
        type: 'ATTESTATION',
        message: 'Demande attestation pour prêt bancaire',
        status: 'in_progress',
        statut: 'EN_COURS',
        priorite: 'NORMALE',
        titre: 'Attestation de travail',
        description: 'Demande attestation pour prêt bancaire',
        typeAttestation: 'travail',
        motifAttestation: 'Demande de prêt bancaire',
        userId: users[2].id,
      },
    }),
  ])

  // Création d'une attestation
  await prisma.attestation.create({
    data: {
      contenu: `Je soussigné, Directeur des Ressources Humaines de l'entreprise RH Manager, certifie par la présente que :

Madame Sophie Bernard,
Poste occupé : Responsable RH
Département : RH

est employée dans notre entreprise depuis le 10 janvier 2018 en qualité de Responsable RH. Cette attestation est délivrée pour servir et valoir ce que de droit.

Fait à Paris, le ${new Date().toLocaleDateString('fr-FR')}

Signature et cachet de l'entreprise
Directeur des Ressources Humaines`,
      statut: 'GENEREE',
      genereParIA: true,
      demandeId: demandes[2].id,
    },
  })

  // Création des templates d'attestations
  await Promise.all([
    prisma.templateAttestation.create({
      data: {
        nom: 'Attestation de travail standard',
        type: 'travail',
        contenu: `Je soussigné, {{directeur_rh}}, certifie par la présente que :

{{civilite}} {{nom}} {{prenom}},
Poste occupé : {{poste}}
Département : {{departement}}

est employé(e) dans notre entreprise depuis le {{date_embauche}} en qualité de {{poste}}. Cette attestation est délivrée pour {{motif}}.

Fait à {{ville}}, le {{date}}

{{signature}}`,
      },
    }),
    prisma.templateAttestation.create({
      data: {
        nom: 'Attestation de salaire',
        type: 'salaire',
        contenu: `Je soussigné, {{directeur_rh}}, certifie par la présente que :

{{civilite}} {{nom}} {{prenom}},
Poste occupé : {{poste}}

perçoit un salaire mensuel brut de {{salaire}} euros. Cette attestation est délivrée à sa demande pour {{motif}}.

Fait à {{ville}}, le {{date}}

{{signature}}`,
      },
    }),
  ])

  // Création des paramètres système
  await Promise.all([
    prisma.parametre.create({
      data: {
        cle: 'nom_entreprise',
        valeur: 'RH Manager',
        description: 'Nom de l\'entreprise',
        type: 'TEXTE',
      },
    }),
    prisma.parametre.create({
      data: {
        cle: 'adresse_entreprise',
        valeur: '123 Rue de la Paix, 75001 Paris',
        description: 'Adresse de l\'entreprise',
        type: 'TEXTE',
      },
    }),
    prisma.parametre.create({
      data: {
        cle: 'email_entreprise',
        valeur: '<EMAIL>',
        description: 'Email de contact de l\'entreprise',
        type: 'EMAIL',
      },
    }),
    prisma.parametre.create({
      data: {
        cle: 'directeur_rh',
        valeur: 'Sophie Bernard',
        description: 'Nom du directeur RH',
        type: 'TEXTE',
      },
    }),
    prisma.parametre.create({
      data: {
        cle: 'max_conges_consecutifs',
        valeur: '30',
        description: 'Nombre maximum de jours de congés consécutifs',
        type: 'NOMBRE',
      },
    }),
    prisma.parametre.create({
      data: {
        cle: 'auto_approve_conges',
        valeur: 'false',
        description: 'Validation automatique des congés',
        type: 'BOOLEEN',
      },
    }),
  ])

  // Création de notifications
  await Promise.all([
    prisma.notification.create({
      data: {
        titre: 'Nouvelle demande de congé',
        message: 'Marie Dubois a soumis une demande de congé du 01/02/2024 au 15/02/2024',
        type: 'DEMANDE',
        userId: users[2].id, // Notification pour le RH
      },
    }),
    prisma.notification.create({
      data: {
        titre: 'Demande approuvée',
        message: 'Votre demande de matériel a été approuvée',
        type: 'VALIDATION',
        lu: true,
        userId: users[1].id,
      },
    }),
  ])

  console.log('✅ Base de données initialisée avec succès!')
  console.log(`📊 Créé: ${users.length} utilisateurs, ${demandes.length} demandes`)
}

main()
  .catch((e) => {
    console.error('❌ Erreur lors du seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
    console.log('🔌 Connexion fermée')
  })
