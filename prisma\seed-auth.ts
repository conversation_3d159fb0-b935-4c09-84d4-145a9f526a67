import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Début du seeding avec authentification...')

  // Supprimer les données existantes
  console.log('🗑️ Suppression des données existantes...')
  await prisma.notification.deleteMany()
  await prisma.attestation.deleteMany()
  await prisma.demande.deleteMany()
  await prisma.session.deleteMany()
  await prisma.account.deleteMany()
  await prisma.user.deleteMany()

  // Hasher les mots de passe
  const adminPassword = await bcrypt.hash('admin123', 12)
  const rhPassword = await bcrypt.hash('rh123', 12)
  const managerPassword = await bcrypt.hash('manager123', 12)
  const employePassword = await bcrypt.hash('employe123', 12)

  // Créer les utilisateurs avec authentification
  console.log('👥 Création des utilisateurs avec authentification...')
  
  const admin = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: 'Admin Système',
      nom: 'Admin',
      prenom: 'Système',
      poste: 'Administrateur Système',
      departement: 'IT',
      role: 'ADMIN',
      statut: 'ACTIF',
      password: adminPassword,
      congesRestants: 25,
    },
  })

  const rh = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: 'Marie Dupont',
      nom: 'Dupont',
      prenom: 'Marie',
      poste: 'Responsable RH',
      departement: 'Ressources Humaines',
      role: 'RH',
      statut: 'ACTIF',
      password: rhPassword,
      congesRestants: 25,
      telephone: '+33 1 23 45 67 89',
      dateEmbauche: new Date('2019-01-15'),
      salaire: 65000,
    },
  })

  const manager = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: 'Pierre Martin',
      nom: 'Martin',
      prenom: 'Pierre',
      poste: 'Chef d\'équipe',
      departement: 'Développement',
      role: 'MANAGER',
      statut: 'ACTIF',
      password: managerPassword,
      congesRestants: 20,
      telephone: '+33 1 23 45 67 90',
      dateEmbauche: new Date('2020-03-01'),
      salaire: 60000,
    },
  })

  const employe = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: 'Sophie Dubois',
      nom: 'Dubois',
      prenom: 'Sophie',
      poste: 'Développeuse',
      departement: 'Développement',
      manager: 'Pierre Martin',
      role: 'EMPLOYE',
      statut: 'ACTIF',
      password: employePassword,
      congesRestants: 25,
      telephone: '+33 1 23 45 67 91',
      dateEmbauche: new Date('2021-06-01'),
      salaire: 45000,
    },
  })

  // Créer quelques employés supplémentaires
  const employe2 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: 'Jean Moreau',
      nom: 'Moreau',
      prenom: 'Jean',
      poste: 'Développeur Senior',
      departement: 'Développement',
      manager: 'Pierre Martin',
      role: 'EMPLOYE',
      statut: 'ACTIF',
      password: employePassword,
      congesRestants: 18,
      telephone: '+33 1 23 45 67 92',
      dateEmbauche: new Date('2019-09-15'),
      salaire: 52000,
    },
  })

  console.log('📋 Création des demandes de test...')
  
  // Créer quelques demandes de test
  await prisma.demande.create({
    data: {
      type: 'CONGE',
      titre: 'Congés d\'été',
      description: 'Demande de congés pour vacances familiales',
      statut: 'EN_ATTENTE',
      priorite: 'NORMALE',
      dateDebut: new Date('2024-07-01'),
      dateFin: new Date('2024-07-15'),
      userId: employe.id,
    },
  })

  await prisma.demande.create({
    data: {
      type: 'MATERIEL',
      titre: 'Nouvel ordinateur portable',
      description: 'Demande d\'un nouvel ordinateur portable pour le développement',
      statut: 'VALIDEE',
      priorite: 'HAUTE',
      materiel: 'MacBook Pro 16"',
      quantite: 1,
      budgetEstime: 2500,
      justification: 'Ordinateur actuel obsolète',
      userId: employe2.id,
    },
  })

  await prisma.demande.create({
    data: {
      type: 'ATTESTATION',
      titre: 'Attestation de travail',
      description: 'Demande d\'attestation de travail pour dossier bancaire',
      statut: 'EN_COURS',
      priorite: 'NORMALE',
      typeAttestation: 'TRAVAIL',
      motifAttestation: 'Dossier bancaire',
      langue: 'français',
      userId: employe.id,
    },
  })

  console.log('🔔 Création des notifications de test...')
  
  // Créer quelques notifications
  await prisma.notification.create({
    data: {
      titre: 'Demande approuvée',
      message: 'Votre demande de matériel a été approuvée',
      type: 'SUCCES',
      userId: employe2.id,
    },
  })

  await prisma.notification.create({
    data: {
      titre: 'Nouvelle demande',
      message: 'Une nouvelle demande de congés nécessite votre attention',
      type: 'INFO',
      userId: manager.id,
    },
  })

  console.log('✅ Seeding terminé avec succès!')
  console.log('\n📝 Comptes créés:')
  console.log('Admin: <EMAIL> / admin123')
  console.log('RH: <EMAIL> / rh123')
  console.log('Manager: <EMAIL> / manager123')
  console.log('Employé: <EMAIL> / employe123')
  console.log('Employé 2: <EMAIL> / employe123')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
