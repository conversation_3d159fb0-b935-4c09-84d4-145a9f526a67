"use client"

import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"

import {
  Sparkles,
  FileText,
  Eye,
  Send,
  Download,
  Plus,
  Calendar,
  User,
  Building,
  Bot,
  CheckCircle,
  XCircle,
  AlertCircle
} from "lucide-react"
import { createAttestation } from "@/lib/actions"
import { toast } from "sonner"

interface AttestationsManagerProps {
  attestations: any[]
  demandes: any[]
}

export function AttestationsManager({ attestations, demandes }: AttestationsManagerProps) {
  const [isGenerating, setIsGenerating] = React.useState(false)
  const [selectedDemande, setSelectedDemande] = React.useState("")
  const [contenuPersonnalise, setContenuPersonnalise] = React.useState("")
  const [previewAttestation, setPreviewAttestation] = React.useState<any>(null)
  const [typeAttestation, setTypeAttestation] = React.useState<'travail' | 'stage'>('travail')
  const [aiStatus, setAiStatus] = React.useState<{
    connected: boolean;
    configured: boolean;
    loading: boolean;
    provider?: string;
    model?: string;
  }>({ connected: false, configured: false, loading: true })
  const [viewingAttestation, setViewingAttestation] = React.useState<any>(null)
  const [isDownloading, setIsDownloading] = React.useState<string | null>(null)
  const [isSending, setIsSending] = React.useState<string | null>(null)

  // Filtrer les demandes d'attestation qui n'ont pas encore d'attestation
  const demandesAttestationSansAttestation = demandes.filter(
    d => d.type === 'ATTESTATION' && !d.attestation
  )

  // Vérifier le statut IA au chargement
  React.useEffect(() => {
    const verifierIA = async () => {
      try {
        const response = await fetch('/api/attestations/generer')
        if (response.ok) {
          const data = await response.json()
          setAiStatus({
            connected: data.groqConnected,
            configured: data.apiKeyConfigured,
            loading: false,
            provider: data.provider,
            model: data.model
          })
        } else {
          setAiStatus({
            connected: false,
            configured: false,
            loading: false
          })
        }
      } catch (error) {
        console.error('Erreur vérification IA:', error)
        setAiStatus({
          connected: false,
          configured: false,
          loading: false
        })
      }
    }

    verifierIA()
  }, [])

  const getStatutBadge = (statut: string) => {
    switch (statut) {
      case "GENEREE":
        return <Badge variant="outline" className="text-blue-600">Générée</Badge>
      case "VALIDEE":
        return <Badge variant="outline" className="text-green-600">Validée</Badge>
      case "ENVOYEE":
        return <Badge variant="outline" className="text-purple-600">Envoyée</Badge>
      case "ARCHIVEE":
        return <Badge variant="outline" className="text-gray-600">Archivée</Badge>
      default:
        return <Badge variant="outline">{statut}</Badge>
    }
  }

  const genererAttestation = async () => {
    if (!selectedDemande) {
      toast.error('Veuillez sélectionner une demande')
      return
    }

    const demande = demandes.find(d => d.id === selectedDemande)
    if (!demande) {
      toast.error('Demande non trouvée')
      return
    }

    setIsGenerating(true)
    try {
      // Utiliser la nouvelle API avec IA Groq
      const response = await fetch('/api/attestations/generer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          demandeId: selectedDemande,
          typeAttestation: typeAttestation,
          contenuPersonnalise: contenuPersonnalise || null,
        }),
      })

      const result = await response.json()

      if (response.ok && result.success) {
        toast.success(
          result.attestation.genereParIA
            ? `Attestation générée avec l'IA ${aiStatus.provider?.toUpperCase()} avec succès !`
            : 'Attestation créée avec succès !'
        )
        setSelectedDemande("")
        setContenuPersonnalise("")
        setPreviewAttestation(null)
        // Recharger la page pour voir la nouvelle attestation
        window.location.reload()
      } else {
        toast.error(result.error || 'Erreur lors de la génération')
      }
    } catch (error) {
      console.error('Erreur:', error)
      toast.error('Erreur lors de la génération')
    } finally {
      setIsGenerating(false)
    }
  }

  const genererContenuAttestation = (demande: any) => {
    const user = demande.user
    const dateActuelle = new Date().toLocaleDateString('fr-FR')

    return `Je soussigné, Directeur des Ressources Humaines de l'entreprise RH Manager, certifie par la présente que :

${user?.prenom ? `${user.prenom} ${user.nom}` : user?.name || 'Nom non disponible'},
${user?.poste ? `Poste occupé : ${user.poste}` : ''}
${user?.departement ? `Département : ${user.departement}` : ''}

est employé(e) dans notre entreprise ${user?.dateEmbauche ? `depuis le ${new Date(user.dateEmbauche).toLocaleDateString('fr-FR')}` : ''} ${user?.poste ? `en qualité de ${user.poste}` : ''}.

Cette attestation est délivrée à sa demande pour ${demande.motifAttestation || 'servir et valoir ce que de droit'}.

Fait à Paris, le ${dateActuelle}

Signature et cachet de l'entreprise
Directeur des Ressources Humaines`
  }

  const previsualiserAttestation = () => {
    if (!selectedDemande) {
      toast.error('Veuillez sélectionner une demande')
      return
    }

    const demande = demandes.find(d => d.id === selectedDemande)
    if (!demande) return

    const contenu = contenuPersonnalise || genererContenuAttestation(demande)
    setPreviewAttestation({ demande, contenu })
  }

  const voirAttestation = (attestation: any) => {
    setViewingAttestation(attestation)
  }

  const telechargerPDF = async (attestation: any) => {
    setIsDownloading(attestation.id)
    try {
      // Créer une nouvelle fenêtre avec le contenu de l'attestation
      const printWindow = window.open('', '_blank')
      if (!printWindow) {
        toast.error('Veuillez autoriser les pop-ups pour télécharger le PDF')
        return
      }

      const htmlContent = `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attestation - ${attestation.demande?.user?.name || 'Document'}</title>
    <style>
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
        body {
            font-family: 'Times New Roman', serif;
            line-height: 1.6;
            margin: 40px;
            color: #333;
            background: white;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .title {
            font-size: 20px;
            font-weight: bold;
            text-align: center;
            margin: 40px 0;
            text-decoration: underline;
        }
        .content {
            white-space: pre-wrap;
            text-align: justify;
            margin: 30px 0;
            font-size: 14px;
        }
        .footer {
            margin-top: 60px;
            text-align: right;
        }
        .signature {
            margin-top: 40px;
            text-align: right;
        }
        .date {
            margin-bottom: 20px;
        }
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .print-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <button class="print-button no-print" onclick="window.print()">Imprimer / Sauvegarder en PDF</button>

    <div class="header">
        <div class="company-name">RH Manager</div>
        <div>Système de Gestion des Ressources Humaines</div>
    </div>

    <div class="title">ATTESTATION DE TRAVAIL</div>

    <div class="content">${attestation.contenu.replace(/\n/g, '<br>')}</div>

    <div class="footer">
        <div class="date">
            Généré le ${new Date().toLocaleDateString('fr-FR')}
        </div>
        <div class="signature">
            <p>Signature et cachet de l'entreprise</p>
            <p><strong>Direction des Ressources Humaines</strong></p>
        </div>
    </div>
</body>
</html>`

      printWindow.document.write(htmlContent)
      printWindow.document.close()

      // Attendre que le contenu soit chargé puis déclencher l'impression
      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.focus()
        }, 250)
      }

      toast.success('Fenêtre d\'impression ouverte - Utilisez Ctrl+P ou le bouton pour imprimer/sauvegarder en PDF')
    } catch (error) {
      console.error('Erreur ouverture impression:', error)
      toast.error('Erreur lors de l\'ouverture de l\'impression')
    } finally {
      setIsDownloading(null)
    }
  }

  const envoyerAttestation = async (attestation: any) => {
    setIsSending(attestation.id)
    try {
      const response = await fetch(`/api/attestations/${attestation.id}/envoyer`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const result = await response.json()

      if (response.ok && result.success) {
        toast.success('Attestation envoyée avec succès')
        // Recharger la page pour mettre à jour le statut
        window.location.reload()
      } else {
        toast.error(result.error || 'Erreur lors de l\'envoi')
      }
    } catch (error) {
      console.error('Erreur envoi:', error)
      toast.error('Erreur lors de l\'envoi')
    } finally {
      setIsSending(null)
    }
  }

  return (
    <div className="space-y-6">
      {/* Statistiques */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{attestations.length}</div>
            <p className="text-xs text-muted-foreground">
              attestations générées
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">En attente</CardTitle>
            <Sparkles className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{demandesAttestationSansAttestation.length}</div>
            <p className="text-xs text-muted-foreground">
              demandes à traiter
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Générées</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {attestations.filter(a => a.statut === 'GENEREE').length}
            </div>
            <p className="text-xs text-muted-foreground">
              prêtes à envoyer
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Envoyées</CardTitle>
            <Send className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {attestations.filter(a => a.statut === 'ENVOYEE').length}
            </div>
            <p className="text-xs text-muted-foreground">
              attestations envoyées
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="generer" className="w-full">
        <TabsList>
          <TabsTrigger value="generer">Générer</TabsTrigger>
          <TabsTrigger value="liste">Liste des attestations</TabsTrigger>
          <TabsTrigger value="preview">Prévisualisation</TabsTrigger>
        </TabsList>

        <TabsContent value="generer">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bot className="h-5 w-5" />
                Générer une nouvelle attestation avec IA
              </CardTitle>
              <CardDescription>
                Sélectionnez une demande et le type d'attestation pour générer automatiquement le document
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Statut IA */}
              {!aiStatus.loading && (
                <Alert className={aiStatus.connected ? "border-green-200 bg-green-50" : "border-orange-200 bg-orange-50"}>
                  <div className="flex items-center gap-2">
                    {aiStatus.connected ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : aiStatus.configured ? (
                      <AlertCircle className="h-4 w-4 text-orange-600" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-600" />
                    )}
                    <AlertDescription className={aiStatus.connected ? "text-green-800" : "text-orange-800"}>
                      {aiStatus.connected
                        ? `✅ IA ${aiStatus.provider?.toUpperCase()} connectée (${aiStatus.model})`
                        : aiStatus.configured
                          ? "⚠️ Clé API configurée mais connexion échouée"
                          : "❌ Clé API Groq non configurée - Génération manuelle uniquement"
                      }
                    </AlertDescription>
                  </div>
                </Alert>
              )}

              <div className="space-y-2">
                <Label htmlFor="demande">Demande d'attestation</Label>
                <Select value={selectedDemande} onValueChange={setSelectedDemande}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner une demande" />
                  </SelectTrigger>
                  <SelectContent>
                    {demandesAttestationSansAttestation.map((demande) => (
                      <SelectItem key={demande.id} value={demande.id}>
                        {demande.user?.name || 'Utilisateur inconnu'} - {demande.typeAttestation || 'Type non spécifié'}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {demandesAttestationSansAttestation.length === 0 && (
                  <p className="text-sm text-muted-foreground">
                    Aucune demande d'attestation en attente
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">Type d'attestation</Label>
                <Select value={typeAttestation} onValueChange={(value: 'travail' | 'stage') => setTypeAttestation(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="travail">
                      <div className="flex items-center gap-2">
                        <Building className="h-4 w-4" />
                        Attestation de travail
                      </div>
                    </SelectItem>
                    <SelectItem value="stage">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        Attestation de stage
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="contenu">Contenu personnalisé (optionnel)</Label>
                <Textarea
                  id="contenu"
                  placeholder={aiStatus.connected
                    ? "Laissez vide pour générer automatiquement avec l'IA..."
                    : "Laissez vide pour générer avec le modèle de base..."
                  }
                  value={contenuPersonnalise}
                  onChange={(e) => setContenuPersonnalise(e.target.value)}
                  rows={6}
                />
                <p className="text-xs text-muted-foreground">
                  {aiStatus.connected
                    ? `🤖 L'IA ${aiStatus.provider?.toUpperCase()} générera automatiquement une attestation professionnelle adaptée au type sélectionné`
                    : "📝 Un modèle de base sera utilisé si vous laissez ce champ vide"
                  }
                </p>
              </div>

              <div className="flex gap-2">
                <Button
                  onClick={genererAttestation}
                  disabled={isGenerating || !selectedDemande}
                  className={aiStatus.connected ? "bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700" : ""}
                >
                  {aiStatus.connected ? (
                    <Bot className="mr-2 h-4 w-4" />
                  ) : (
                    <Sparkles className="mr-2 h-4 w-4" />
                  )}
                  {isGenerating
                    ? 'Génération...'
                    : aiStatus.connected
                      ? `Générer avec ${aiStatus.provider?.toUpperCase()}`
                      : 'Générer l\'attestation'
                  }
                </Button>
                <Button
                  variant="outline"
                  onClick={previsualiserAttestation}
                  disabled={!selectedDemande}
                >
                  <Eye className="mr-2 h-4 w-4" />
                  Prévisualiser
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="liste">
          <Card>
            <CardHeader>
              <CardTitle>Attestations générées</CardTitle>
              <CardDescription>
                Liste de toutes les attestations créées
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {attestations.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    Aucune attestation générée
                  </div>
                ) : (
                  attestations.map((attestation) => (
                    <div key={attestation.id} className="border rounded-lg p-4 space-y-3">
                      <div className="flex items-start justify-between">
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium">
                              {attestation.demande?.user?.name || 'Utilisateur inconnu'}
                            </h4>
                            {getStatutBadge(attestation.statut)}
                            {attestation.genereParIA && (
                              <Badge variant="secondary" className="text-xs">
                                <Sparkles className="mr-1 h-3 w-3" />
                                IA
                              </Badge>
                            )}
                          </div>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <span className="flex items-center gap-1">
                              <User className="h-3 w-3" />
                              {attestation.demande?.user?.poste || 'Poste non défini'}
                            </span>
                            <span className="flex items-center gap-1">
                              <Building className="h-3 w-3" />
                              {attestation.demande?.user?.departement || 'Département non défini'}
                            </span>
                            <span className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              {new Date(attestation.createdAt).toLocaleDateString('fr-FR')}
                            </span>
                          </div>
                        </div>

                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => voirAttestation(attestation)}
                          >
                            <Eye className="mr-2 h-4 w-4" />
                            Voir
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => telechargerPDF(attestation)}
                            disabled={isDownloading === attestation.id}
                          >
                            <Download className="mr-2 h-4 w-4" />
                            {isDownloading === attestation.id ? 'Téléchargement...' : 'PDF'}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => envoyerAttestation(attestation)}
                            disabled={isSending === attestation.id || attestation.statut === 'ENVOYEE'}
                          >
                            <Send className="mr-2 h-4 w-4" />
                            {isSending === attestation.id ? 'Envoi...' : attestation.statut === 'ENVOYEE' ? 'Envoyée' : 'Envoyer'}
                          </Button>
                        </div>
                      </div>

                      <div className="text-sm text-muted-foreground bg-muted p-3 rounded">
                        {attestation.contenu.substring(0, 200)}...
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="preview">
          <Card>
            <CardHeader>
              <CardTitle>Prévisualisation</CardTitle>
              <CardDescription>
                Aperçu de l'attestation avant génération
              </CardDescription>
            </CardHeader>
            <CardContent>
              {previewAttestation ? (
                <div className="space-y-4">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <User className="h-4 w-4" />
                    <span>{previewAttestation.demande.user?.name}</span>
                    <span>•</span>
                    <span>{previewAttestation.demande.user?.poste}</span>
                    <span>•</span>
                    <span>{previewAttestation.demande.user?.departement}</span>
                  </div>

                  <div className="border rounded-lg p-6 bg-white">
                    <pre className="whitespace-pre-wrap font-serif text-sm leading-relaxed">
                      {previewAttestation.contenu}
                    </pre>
                  </div>

                  <div className="flex gap-2">
                    <Button onClick={genererAttestation} disabled={isGenerating}>
                      <Plus className="mr-2 h-4 w-4" />
                      Confirmer et générer
                    </Button>
                    <Button variant="outline" onClick={() => setPreviewAttestation(null)}>
                      Fermer
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  Sélectionnez une demande et cliquez sur "Prévisualiser" pour voir l'aperçu
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Modal pour voir l'attestation */}
      {viewingAttestation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">
                  Attestation - {viewingAttestation.demande?.user?.name || 'Utilisateur inconnu'}
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setViewingAttestation(null)}
                >
                  ✕
                </Button>
              </div>

              <div className="space-y-4">
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <User className="h-3 w-3" />
                    {viewingAttestation.demande?.user?.poste || 'Poste non défini'}
                  </span>
                  <span className="flex items-center gap-1">
                    <Building className="h-3 w-3" />
                    {viewingAttestation.demande?.user?.departement || 'Département non défini'}
                  </span>
                  <span className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {new Date(viewingAttestation.createdAt).toLocaleDateString('fr-FR')}
                  </span>
                  {getStatutBadge(viewingAttestation.statut)}
                </div>

                <div className="border rounded-lg p-6 bg-gray-50">
                  <pre className="whitespace-pre-wrap font-serif text-sm leading-relaxed">
                    {viewingAttestation.contenu}
                  </pre>
                </div>

                <div className="flex gap-2 justify-end">
                  <Button
                    variant="outline"
                    onClick={() => telechargerPDF(viewingAttestation)}
                    disabled={isDownloading === viewingAttestation.id}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    {isDownloading === viewingAttestation.id ? 'Téléchargement...' : 'Télécharger PDF'}
                  </Button>
                  <Button
                    onClick={() => envoyerAttestation(viewingAttestation)}
                    disabled={isSending === viewingAttestation.id || viewingAttestation.statut === 'ENVOYEE'}
                  >
                    <Send className="mr-2 h-4 w-4" />
                    {isSending === viewingAttestation.id ? 'Envoi...' : viewingAttestation.statut === 'ENVOYEE' ? 'Déjà envoyée' : 'Envoyer'}
                  </Button>
                  <Button variant="outline" onClick={() => setViewingAttestation(null)}>
                    Fermer
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
